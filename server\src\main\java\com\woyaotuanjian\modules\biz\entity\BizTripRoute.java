package com.woyaotuanjian.modules.biz.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 行程路线配置
 */
@Data
@TableName("biz_trip_route")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="biz_trip_route对象", description="行程路线配置")
public class BizTripRoute {
    
    /**主键ID*/
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    /**行程ID*/
    @ApiModelProperty(value = "行程ID")
    private Long tripId;
    
    /**起点ID*/
    @ApiModelProperty(value = "起点ID")
    private Long originId;
    
    /**起点类型*/
    @ApiModelProperty(value = "起点类型(scenic/hotel/rest/word)")
    private String originType;
    
    /**终点ID*/
    @ApiModelProperty(value = "终点ID")
    private Long destinationId;
    
    /**终点类型*/
    @ApiModelProperty(value = "终点类型(scenic/hotel/rest/word)")
    private String destinationType;
    
    /**交通方式*/
    @ApiModelProperty(value = "交通方式(driving/railway/flight/ship)")
    private String transportType;
    
    /**时长(分钟)*/
    @ApiModelProperty(value = "时长(分钟)")
    private Integer duration;
    
    /**距离(公里)*/
    @ApiModelProperty(value = "距离(公里)")
    private BigDecimal distance;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
} 