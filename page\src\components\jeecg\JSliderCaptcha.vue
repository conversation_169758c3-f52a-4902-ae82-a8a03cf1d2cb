<template>
  <div class="slider-captcha" :style="captchaContainerStyle">
    <div 
      class="slider-bg" 
      ref="sliderBg"
      :style="{ height: height + 'px', width: width + 'px' }"
    >
      <div class="slider-img-div" v-if="imagesReady">
        <img 
          :src="bgImgUrl" 
          class="slider-bg-img" 
          alt="背景带缺口"
          ref="bgImg" 
          :style="{ height: height + 'px', width: width + 'px' }"
          @error="handleImgErrorLoadingBgWithHole"
        />
        <!-- Draggable Block -->
        <div 
          class="slider-block" 
          ref="block"
          :style="{
            left: sliderLeft + 'px',
            top: blockTargetY + 'px',
            width: blockSize + 'px',
            height: blockSize + 'px',
            backgroundImage: `url(${blockPieceImgUrl})`,
            backgroundPosition: '0 0',
            backgroundSize: `${blockSize}px ${blockSize}px`
          }"
        ></div>
        
      </div>
      <div v-else-if="!hasError" class="slider-loading-placeholder" :style="{ height: height + 'px', width: width + 'px' }">
        <a-spin tip="验证码加载中..."></a-spin>
      </div>
      <div v-else class="slider-error-placeholder" :style="{ height: height + 'px', width: width + 'px' }">
        <div class="error-content">
          <a-icon type="exclamation-circle" style="font-size: 24px; color: #ff4d4f; margin-bottom: 8px;" />
          <p>验证码加载失败</p>
          <a-button type="primary" size="small" @click="refresh">重新加载</a-button>
        </div>
      </div>
      <div class="slider-bottom" :class="{ 'success': verified, 'error': !verified && isMouseDown === false && sliderLeft > 0 }">
        <div 
          class="slider-btn" 
          ref="sliderBtn"
          @mousedown="handleMouseDown"
          @touchstart="handleTouchStart"
          :style="{ left: sliderLeft + 'px' }"
        >
          <a-icon :type="sliderIcon" />
        </div>
        <span class="slider-text">{{ sliderText }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage';

export default {
  name: 'JSliderCaptcha',
  props: {
    width: {
      type: Number,
      default: 310
    },
    height: {
      type: Number,
      default: 155
    },
    blockSize: {
      type: Number,
      default: 42 
    },
    deviation: {
      type: Number,
      default: 15
    }
  },
  data() {
    return {
      bgImgUrl: '',
      blockPieceImgUrl: '',
      imagesReady: false,
      hasError: false, // 错误状态标记
      
      blockTargetX: 0, // 从后端获取的正确位置
      blockTargetY: 0,
      
      sliderLeft: 0,
      originX: 0,
      isMouseDown: false,
      trail: [],
      trajectory: [], // 轨迹数据
      startTime: null, // 开始时间
      verified: false,
      sliderText: '', 
      sliderIcon: 'drag',
      captchaId: ''
    }
  },
  mounted() {
    this.initCaptcha();
    document.addEventListener('mousemove', this.handleMouseMove, { passive: false });
    document.addEventListener('mouseup', this.handleMouseUp);
    document.addEventListener('touchmove', this.handleTouchMove, { passive: false });
    document.addEventListener('touchend', this.handleTouchEnd);
  },
  beforeDestroy() {
    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('mouseup', this.handleMouseUp);
    document.removeEventListener('touchmove', this.handleTouchMove);
    document.removeEventListener('touchend', this.handleTouchEnd);
  },
  methods: {
    initCaptcha() {
      this.imagesReady = false;
      this.hasError = false; // 重置错误状态
      this.sliderLeft = 0;
      this.verified = false;
      this.isMouseDown = false;
      this.sliderText = ''; 
      this.sliderIcon = 'drag';
      this.trail = [];
      this.trajectory = []; // 重置轨迹数据
      this.startTime = null; // 重置开始时间
      this.bgImgUrl = ''; 
      this.blockPieceImgUrl = ''; 
      this.getCaptchaFromServer();
    },

    createBaseImageContent(canvas) {
      const ctx = canvas.getContext('2d');
      canvas.width = this.width;
      canvas.height = this.height;

      const gradient = ctx.createLinearGradient(0, 0, this.width, this.height);
      gradient.addColorStop(0, this.getRandomColor(200, 240));
      gradient.addColorStop(1, this.getRandomColor(180, 220));
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, this.width, this.height);

      for (let i = 0; i < 20; i++) {
        ctx.beginPath();
        ctx.fillStyle = this.getRandomColor(100, 200, 0.5);
        if (Math.random() > 0.5) {
          ctx.arc(Math.random() * this.width, Math.random() * this.height, 5 + Math.random() * 15, 0, Math.PI * 2);
        } else {
          ctx.rect(Math.random() * this.width, Math.random() * this.height, 10 + Math.random() * 30, 10 + Math.random() * 30);
        }
        ctx.fill();
      }
    },

    generateCaptchaImages(sourceGeneratorFunc) {
        const padding = 5;
        // 使用后端返回的blockTargetX，只随机生成Y坐标
        this.blockTargetY = Math.floor(Math.random() * (this.height - this.blockSize - padding * 2)) + padding;

        const originalImageCanvas = document.createElement('canvas');
        try {
            sourceGeneratorFunc(originalImageCanvas);
        } catch (e) {
            console.error('源图像生成错误:', e);
            this.hasError = true;
            this.$message.error('验证码图像生成失败，请刷新重试');
            this.$emit('error', '图像生成失败');
            return;
        }
        
        const blockCanvas = document.createElement('canvas');
        blockCanvas.width = this.blockSize;
        blockCanvas.height = this.blockSize;
        const blockCtx = blockCanvas.getContext('2d');
        try {
            blockCtx.drawImage(originalImageCanvas, 
                               this.blockTargetX, this.blockTargetY, this.blockSize, this.blockSize,
                               0, 0, this.blockSize, this.blockSize);
            const generatedBlockUrl = blockCanvas.toDataURL('image/png');
            if (!generatedBlockUrl || !generatedBlockUrl.startsWith('data:image/png;base64,') || generatedBlockUrl.length < 100) {
                console.error('无效拼图块URL');
                this.hasError = true;
                this.$message.error('验证码滑块生成失败，请刷新重试');
                this.$emit('error', '滑块生成失败');
                return;
            }
            this.blockPieceImgUrl = generatedBlockUrl;
        } catch (e) {
            console.error('拼图块生成错误:', e);
            this.hasError = true;
            this.$message.error('验证码滑块生成失败，请刷新重试');
            this.$emit('error', '滑块生成失败');
            return;
        }

        const backgroundWithHoleCanvas = document.createElement('canvas');
        try {
            sourceGeneratorFunc(backgroundWithHoleCanvas); 
            const bgHoleCtx = backgroundWithHoleCanvas.getContext('2d');
            bgHoleCtx.fillStyle = 'rgba(0, 0, 0, 0.4)'; 
            bgHoleCtx.fillRect(this.blockTargetX, this.blockTargetY, this.blockSize, this.blockSize);
            bgHoleCtx.strokeStyle = 'rgba(255, 255, 255, 0.8)'; 
            bgHoleCtx.lineWidth = 2;
            bgHoleCtx.strokeRect(this.blockTargetX + 1, this.blockTargetY + 1, this.blockSize - 2, this.blockSize - 2);
            const generatedBgUrl = backgroundWithHoleCanvas.toDataURL('image/png');
            if (!generatedBgUrl || !generatedBgUrl.startsWith('data:image/png;base64,') || generatedBgUrl.length < 100) {
                console.error('无效背景URL');
                this.hasError = true;
                this.$message.error('验证码背景生成失败，请刷新重试');
                this.$emit('error', '背景生成失败');
                return;
            }
            this.bgImgUrl = generatedBgUrl;
        } catch (e) {
            console.error('带缺口背景生成错误:', e);
            this.hasError = true;
            this.$message.error('验证码背景生成失败，请刷新重试');
            this.$emit('error', '背景生成失败');
            return;
        }
        
        this.imagesReady = true;
    },

    getCaptchaFromServer() {
      getAction('/sys/captcha/slider').then(res => {
        if (res.success) {
          this.hasError = false; // 清除错误状态
          this.captchaId = res.result.captchaId;
          this.blockTargetX = res.result.blockX;
          this.generateCaptchaImages(this.createBaseImageContent);
        } else {
          this.hasError = true; // 设置错误状态
          // 根据错误信息给出不同的提示
          if (res.message && res.message.includes('频繁')) {
            this.$message.error('验证码请求过于频繁，请稍后再试');
          } else {
            this.$message.error('获取验证码失败：' + (res.message || '服务异常'));
          }
          this.$emit('error', res.message || '获取验证码失败');
        }
      }).catch((error) => {
        this.hasError = true; // 设置错误状态
        console.error('获取验证码网络错误:', error);
        this.$message.error('网络连接异常，请检查网络后重试');
        this.$emit('error', '网络连接异常');
      });
    },

    getRandomColor(min, max, alpha = 1) {
      const r = min + Math.floor(Math.random() * (max - min));
      const g = min + Math.floor(Math.random() * (max - min));
      const b = min + Math.floor(Math.random() * (max - min));
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    },

    handleImgErrorLoadingBgWithHole(event) {
      console.error('验证码图片加载错误');
      this.hasError = true;
      this.$message.error('验证码图片加载失败，请刷新重试');
      this.$emit('error', '图片加载失败');
    },

    handleMouseDown(e) {
      if (this.verified) return;
      e.preventDefault();
      this.isMouseDown = true;
      this.originX = e.clientX;
      this.sliderIcon = 'loading';
      this.trail = [this.sliderLeft];
      // 记录开始时间和初始轨迹点
      this.startTime = Date.now();
      this.trajectory = [{
        x: this.sliderLeft,
        y: 0, // 滑块只在水平方向移动
        t: this.startTime
      }];
    },
    handleTouchStart(e) {
      if (this.verified) return;
      this.isMouseDown = true;
      this.originX = e.touches[0].clientX;
      this.sliderIcon = 'loading';
      this.trail = [this.sliderLeft];
      // 记录开始时间和初始轨迹点
      this.startTime = Date.now();
      this.trajectory = [{
        x: this.sliderLeft,
        y: 0,
        t: this.startTime
      }];
    },
    handleMouseMove(e) {
      if (!this.isMouseDown) return;
      e.preventDefault(); 
      const moveX = e.clientX - this.originX;
      let newSliderLeft = this.trail[0] + moveX; 
      const maxSliderLeft = this.width - (this.$refs.sliderBtn ? this.$refs.sliderBtn.offsetWidth : this.blockSize); 
      newSliderLeft = Math.max(0, Math.min(newSliderLeft, maxSliderLeft));
      this.sliderLeft = newSliderLeft;
      
      // 记录轨迹点（限制频率，避免数据过多）
      const now = Date.now();
      if (this.trajectory.length === 0 || now - this.trajectory[this.trajectory.length - 1].t > 10) {
        this.trajectory.push({
          x: Math.round(newSliderLeft),
          y: 0,
          t: now
        });
      }
    },
    handleTouchMove(e) {
      if (!this.isMouseDown) return;
      e.preventDefault(); 
      const moveX = e.touches[0].clientX - this.originX;
      let newSliderLeft = this.trail[0] + moveX;
      const maxSliderLeft = this.width - (this.$refs.sliderBtn ? this.$refs.sliderBtn.offsetWidth : this.blockSize);
      newSliderLeft = Math.max(0, Math.min(newSliderLeft, maxSliderLeft));
      this.sliderLeft = newSliderLeft;
      
      // 记录轨迹点（限制频率，避免数据过多）
      const now = Date.now();
      if (this.trajectory.length === 0 || now - this.trajectory[this.trajectory.length - 1].t > 10) {
        this.trajectory.push({
          x: Math.round(newSliderLeft),
          y: 0,
          t: now
        });
      }
    },
    handleMouseUp() {
      if (!this.isMouseDown) return;
      this.isMouseDown = false;
      this.sliderIcon = this.verified ? 'check' : 'drag';
      if (!this.verified) {
         this.verifyCaptcha();
      }
    },
    handleTouchEnd() {
      if (!this.isMouseDown) return;
      this.isMouseDown = false;
      this.sliderIcon = this.verified ? 'check' : 'drag';
       if (!this.verified) {
         this.verifyCaptcha();
      }
    },
    verifyCaptcha() {
      if (!this.captchaId) {
        this.$message.error('验证码ID丢失，请刷新重试');
        this.reset(); 
        return;
      }

      const offsetXToSend = Math.round(this.sliderLeft);
      
      // 构建轨迹数据字符串 格式: "x1,y1,t1;x2,y2,t2;..."
      let trajectoryStr = '';
      if (this.trajectory && this.trajectory.length > 0) {
        trajectoryStr = this.trajectory.map(point => `${point.x},${point.y},${point.t}`).join(';');
      }
      
      // 构建请求参数
      const params = {
        captchaId: this.captchaId,
        offsetX: offsetXToSend,
        startTime: this.startTime,
        trajectory: trajectoryStr
      };
      
      // 使用POST方式发送，避免URL过长
      postAction('/sys/captcha/verify', params)
      .then(res => {
        if (res.success && res.result) {
            this.verified = true;
            this.sliderText = '验证通过';
            this.sliderIcon = 'check';
            this.$emit('success', { verified: true, captchaId: this.captchaId, offsetX: offsetXToSend });
        } else {
            this.verified = false;
            this.sliderText = res.message || '验证失败'; 
            this.sliderIcon = 'close';
            this.$emit('fail');
            setTimeout(() => this.reset(), 800); 
        }
      }).catch((err) => {
        this.verified = false;
        this.sliderText = err.message || '网络错误';
        this.sliderIcon = 'close';
        this.$emit('fail');
        setTimeout(() => this.reset(), 800);
      });
    },
    reset() {
      this.sliderLeft = 0;
      this.verified = false;
      this.isMouseDown = false;
      this.sliderText = '';
      this.sliderIcon = 'drag';
      this.trail = [];
      this.trajectory = [];
      this.startTime = null;
      
      // 重新获取验证码，但不要在错误状态下自动重试
      if (!this.hasError) {
        this.getCaptchaFromServer();
      }
    },
    
    // 手动刷新验证码（供外部调用）
    refresh() {
      this.initCaptcha();
    }
  },
  computed: {
    captchaContainerStyle() {
      return {
        width: this.width + 'px', // 动态设置为与验证码图片相同的宽度
        maxWidth: '100%', // 响应式设计
        margin: '0 auto',
        position: 'relative',
        userSelect: 'none'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.slider-captcha {
  position: relative;
  margin: 0 auto;
  user-select: none;
  width: 300px; // 设置固定宽度，与验证码图片宽度一致
  max-width: 100%; // 响应式设计，在小屏幕上不超出容器
  
  .slider-bg {
    position: relative;
    background-color: #f0f2f5; 
    border-radius: 6px; 
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15); 
    overflow: hidden;
    width: 100%; // 确保完全填充父容器
    
    .slider-img-div {
      position: relative;
      width: 100%;
      height: 100%; 
      overflow: hidden;
      
      .slider-bg-img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: fill;
        object-position: left top;
      }
      
      .slider-block {
        position: absolute;
        background-repeat: no-repeat;
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.7); 
        border-radius: 3px;
        z-index: 10;
      }
    }
    
    .slider-bottom {
      position: absolute; 
      bottom: 0;
      left: 0;
      width: 100%;
      height: 44px; 
      background-color: rgba(230, 230, 230, 0.7); // Lighter, more translucent bar
      line-height: 44px;
      text-align: center;
      color: #333;
      border-top: 1px solid rgba(0,0,0,0.1);
      border-radius: 0 0 6px 6px; 
      box-sizing: border-box;
      
      &.success {
        background-color: rgba(72, 187, 120, 0.85); 
        border-color: #48BB78;
        color: #fff;
        .slider-text {
            color: #fff;
        }
      }
      
      &.error {
        background-color: rgba(229, 62, 62, 0.85); 
        border-color: #E53E3E;
        color: #fff;
        .slider-text {
            color: #fff;
        }
      }
      
      .slider-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0; 
        width: 40px; 
        height: 40px; 
        background-color: #13C2C2; // 主题色
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
        cursor: grab; 
        color: #fff;
        z-index: 12;
        transition: background-color 0.2s, box-shadow 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px; 
        border: 1px solid #0FB5B5; // 主题色深色版本
        
        &:hover {
            background-color: #0FB5B5; // hover时使用深色版本
        }
        
        &:active {
            cursor: grabbing; 
            background-color: #0FB5B5; // 激活时使用深色版本
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
        }

        .anticon {
            font-size: 20px; 
        }
      }
      
      .slider-text {
        position: relative;
        z-index: 9;
        font-size: 14px;
        color: #555; // Slightly darker for better readability on light bar
        transition: color 0.3s;
        // Display text only when verified or error
        visibility: hidden;
        &.show-text {
           visibility: visible;
        }
      }
       // Default text for the slider track when not verified/error
      &:not(.success):not(.error)::before {
        content: '向右拖动滑块填充拼图';
        position: absolute;
        left: 50px; // Position it after the button
        right: 10px;
        top: 0;
        line-height: 44px;
        font-size: 14px;
        color: #777;
        text-align: center;
        z-index: 8;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .slider-loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f0f2f5;
    border-radius: 6px;
  }
  
  .slider-error-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 6px;
    
    .error-content {
      text-align: center;
      color: #666;
      
      p {
        margin: 8px 0 12px 0;
        font-size: 14px;
      }
    }
  }
}
</style> 