<template>
  <div>
    <!-- 腾讯地图选点模态框 -->
    <a-modal 
      :visible="visible" 
      title="地图选点" 
      @ok="confirmMapPicker" 
      @cancel="closeMapPicker" 
      width="800px" 
      :maskClosable="false"
    >
      <div class="map-search-box" style="margin-bottom: 10px;">
        <a-input 
          v-model="searchKeyword" 
          placeholder="输入地点名称" 
          style="width: 300px;" 
          @pressEnter="searchLocation" 
        />
        <a-button type="primary" style="margin-left: 10px;" @click="searchLocation">搜索</a-button>
      </div>
      
      <!-- 使用腾讯地图Vue组件 -->
      <div style="width: 100%; height: 450px;">
        <tlbs-map
          ref="mapRef"
          api-key="DVSBZ-PL265-5UCIM-IS4ZZ-5DWQJ-IVBUL"
          :center="mapCenter"
          :zoom="13"
          :control="mapControl"
          @click="onMapClick"
          @map_inited="onMapInited"
        >
          <template v-if="longitude && latitude">
            <tlbs-multi-marker
              :geometries="markerGeometries"
              :styles="markerStyles"
            />
          </template>
        </tlbs-map>
      </div>
      
      <div style="margin-top: 10px;">
        <p>当前选择坐标：{{ longitude ? longitude.toFixed(6) : '0.000000' }}, {{ latitude ? latitude.toFixed(6) : '0.000000' }}</p>
        <p style="color: #999;">提示：点击地图可选择位置，或在上方搜索框输入地点名称</p>
      </div>
    </a-modal>
    
    <!-- 搜索结果选择模态框 -->
    <a-modal 
      :visible="searchResultsVisible" 
      title="选择搜索结果" 
      @ok="confirmSearchResult" 
      @cancel="cancelSearchResult" 
      width="600px"
    >
      <div style="max-height: 400px; overflow-y: auto;">
        <a-radio-group v-model="selectedSearchIndex" style="width: 100%;">
          <div v-for="(poi, index) in searchResults" :key="index" style="margin-bottom: 12px;">
            <a-radio :value="index" style="width: 100%;">
              <div style="margin-left: 8px;">
                <div style="font-weight: bold; color: #1890ff;">{{ poi.title }}</div>
                <div style="color: #666; font-size: 12px; margin-top: 2px;">
                  <a-tag v-if="poi.category" color="blue" size="small">{{ poi.category }}</a-tag>
                  <span style="margin-left: 8px;">{{ poi.address || getLocationText(poi.ad_info) }}</span>
                </div>
                <div v-if="poi._distance" style="color: #999; font-size: 11px; margin-top: 2px;">
                  距离中心点：{{ Math.round(poi._distance) }}米
                </div>
              </div>
            </a-radio>
          </div>
        </a-radio-group>
      </div>
      
      <template slot="footer">
        <a-button @click="cancelSearchResult">取消</a-button>
        <a-button type="primary" @click="confirmSearchResult">确定选择</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'MapPicker',
  props: {
    // 初始经度
    initialLongitude: {
      type: Number,
      default: null
    },
    // 初始纬度
    initialLatitude: {
      type: Number,
      default: null
    },
    // 搜索关键词
    initialKeyword: {
      type: String,
      default: ''
    },
    // 搜索分类过滤
    searchCategory: {
      type: String,
      default: ''
    },
    // 搜索区域
    searchRegion: {
      type: String,
      default: '全国'
    }
  },
  data() {
    return {
      visible: false,
      longitude: null,
      latitude: null,
      searchKeyword: '',
      mapCenter: { lng: 116.397470, lat: 39.908491 },
      mapControl: {
        scale: true,
        zoom: {
          position: 'topRight'
        }
      },
      markerGeometries: [],
      markerStyles: {
        marker: {
          width: 25,
          height: 35,
          anchor: { x: 12.5, y: 35 }
        }
      },
      selectedSearchIndex: 0,
      searchResultsVisible: false,
      searchResults: [],
    };
  },
  watch: {
    // 监听初始坐标变化
    initialLongitude(newVal) {
      console.log('MapPicker watch - initialLongitude changed:', newVal);
      if (this.visible && newVal !== null && newVal !== undefined) {
        this.longitude = newVal;
      }
    },
    initialLatitude(newVal) {
      console.log('MapPicker watch - initialLatitude changed:', newVal);
      if (this.visible && newVal !== null && newVal !== undefined) {
        this.latitude = newVal;
      }
    }
  },
  methods: {
    // 显示地图选点器
    show() {
      this.visible = true;
      this.longitude = this.initialLongitude;
      this.latitude = this.initialLatitude;
      this.searchKeyword = this.initialKeyword;
      
      console.log('MapPicker show - 接收到的初始坐标:', this.initialLongitude, this.initialLatitude);
      console.log('MapPicker show - 设置的坐标:', this.longitude, this.latitude);
      
      // 如果已有坐标，设置为地图中心点
      if (this.longitude && this.latitude) {
        this.mapCenter = {
          lng: this.longitude,
          lat: this.latitude
        };
        
        // 设置标记
        this.markerGeometries = [{
          id: 'current',
          styleId: 'marker',
          position: {
            lng: this.longitude,
            lat: this.latitude
          }
        }];
        console.log('MapPicker show - 设置了地图中心和标记');
      } else {
        // 默认中心点（北京）
        this.mapCenter = { lng: 116.397470, lat: 39.908491 };
        this.markerGeometries = [];
        console.log('MapPicker show - 使用默认中心点');
      }
      
      // 强制更新地图视图
      this.$nextTick(() => {
        if (this.$refs.mapRef && this.$refs.mapRef.map && this.longitude && this.latitude) {
          this.$refs.mapRef.map.setCenter({ lng: this.longitude, lat: this.latitude });
          this.$refs.mapRef.map.setZoom(15);
          console.log('MapPicker show - 强制更新地图中心');
        }
      });
    },
    
    // 关闭地图选点器
    closeMapPicker() {
      this.visible = false;
    },
    
    // 确认选择
    confirmMapPicker() {
      if (!this.longitude || !this.latitude) {
        this.$message.warning('请先在地图上选择位置');
        return;
      }
      
      console.log('MapPicker confirmMapPicker - 准备发送坐标:', this.longitude, this.latitude);
      
      // 触发选择事件
      this.$emit('select', {
        longitude: this.longitude,
        latitude: this.latitude
      });
      
      console.log('MapPicker confirmMapPicker - 已发送select事件');
      
      this.visible = false;
    },
    
    // 地图初始化完成
    onMapInited() {
      console.log('地图初始化完成');
      
      // 如果有关键词且没有坐标，自动搜索
      if (this.searchKeyword && (!this.longitude || !this.latitude)) {
        setTimeout(() => {
          this.searchLocation();
        }, 500);
      }
    },
    
    // 地图点击事件
    onMapClick(e) {
      console.log('MapPicker onMapClick - 地图点击事件:', e.latLng);
      
      // 更新坐标
      this.longitude = e.latLng.lng;
      this.latitude = e.latLng.lat;
      
      console.log('MapPicker onMapClick - 更新后的坐标:', this.longitude, this.latitude);
      
      // 更新标记
      this.markerGeometries = [{
        id: 'current',
        styleId: 'marker',
        position: {
          lng: e.latLng.lng,
          lat: e.latLng.lat
        }
      }];
    },
    
    // 搜索位置
    searchLocation() {
      if (!this.searchKeyword) {
        this.$message.warning('请输入搜索关键词');
        return;
      }
      
      this.$message.loading({ content: '正在搜索...', key: 'searchLoading' });
      
      const key = 'DVSBZ-PL265-5UCIM-IS4ZZ-5DWQJ-IVBUL';
      
      let url = `https://apis.map.qq.com/ws/place/v1/search?key=${key}&keyword=${encodeURIComponent(this.searchKeyword)}`;
      url += `&boundary=region(${encodeURIComponent(this.searchRegion)},1)`;
      url += `&page_size=10&page_index=1`;
      
      // 如果有分类过滤，添加过滤条件
      if (this.searchCategory) {
        url += `&filter=category=${encodeURIComponent(this.searchCategory)}`;
      }
      
      url += `&output=jsonp`;
      
      const script = document.createElement('script');
      const callbackName = 'mapSearchCallback_' + Math.random().toString(36).substr(2, 9);
      
      window[callbackName] = (res) => {
        document.body.removeChild(script);
        delete window[callbackName];
        
        if (res && res.status === 0) {
          // 检查是否返回的是城市统计结果
          if (res.data && res.data.length > 0) {
            // 判断是否为城市统计结果（通常城市统计结果的数据结构不同，没有location字段）
            const firstItem = res.data[0];
            if (!firstItem.location && firstItem.city && firstItem.count) {
              // 这是城市统计结果，提示用户添加地域关键词
              this.$message.warning({ 
                content: '搜索范围过大，请在关键词中添加具体的城市或地区名称，如"北京 天安门"', 
                key: 'searchLoading' 
              });
            } else {
              this.handleSearchResults(res.data);
            }
          } else {
            this.$message.warning({ 
              content: '未找到相关位置，请尝试其他关键词', 
              key: 'searchLoading' 
            });
          }
        } else {
          this.$message.warning({ 
            content: res.message || '搜索失败，请重试', 
            key: 'searchLoading' 
          });
        }
      };
      
      script.onerror = () => {
        document.body.removeChild(script);
        delete window[callbackName];
        this.$message.error({ 
          content: '搜索服务连接失败', 
          key: 'searchLoading' 
        });
      };
      
      script.src = url + '&callback=' + callbackName;
      document.body.appendChild(script);
    },
    
    // 处理搜索结果
    handleSearchResults(data) {
      if (data.length === 1) {
        this.selectSearchResult(data[0]);
      } else {
        this.showSearchResultsModal(data);
      }
    },
    
    // 选择搜索结果
    selectSearchResult(poi) {
      const location = poi.location;
      
      this.mapCenter = {
        lng: location.lng,
        lat: location.lat
      };
      
      this.longitude = location.lng;
      this.latitude = location.lat;
      
      // 触发地址信息事件
      this.$emit('address-info', {
        address: poi.address,
        adInfo: poi.ad_info,
        title: poi.title,
        category: poi.category
      });
      
      this.markerGeometries = [];
      this.$nextTick(() => {
        this.markerGeometries = [{
          id: 'search',
          styleId: 'marker',
          position: {
            lng: location.lng,
            lat: location.lat
          }
        }];
        
        if (this.$refs.mapRef && this.$refs.mapRef.map) {
          this.$refs.mapRef.map.setCenter(this.mapCenter);
          this.$refs.mapRef.map.setZoom(15);
        }
      });
      
      let successMsg = `已定位到：${poi.title}`;
      if (poi.category) {
        successMsg += ` (${poi.category})`;
      }
      this.$message.success({ content: successMsg, key: 'searchLoading' });
    },
    
    // 显示搜索结果选择模态框
    showSearchResultsModal(data) {
      this.$message.destroy('searchLoading');
      
      // 根据搜索分类排序
      if (this.searchCategory) {
        const categories = this.searchCategory.split(',');
        const sortedData = data.sort((a, b) => {
          const aMatch = a.category && categories.some(cat => a.category.includes(cat));
          const bMatch = b.category && categories.some(cat => b.category.includes(cat));
          
          if (aMatch && !bMatch) return -1;
          if (!aMatch && bMatch) return 1;
          return 0;
        });
        this.searchResults = sortedData.slice(0, 8);
      } else {
        this.searchResults = data.slice(0, 8);
      }
      
      this.selectedSearchIndex = 0;
      this.searchResultsVisible = true;
    },
    
    // 确认搜索结果选择
    confirmSearchResult() {
      if (this.searchResults.length > 0 && this.selectedSearchIndex >= 0) {
        this.selectSearchResult(this.searchResults[this.selectedSearchIndex]);
      }
      this.searchResultsVisible = false;
    },
    
    // 取消搜索结果选择
    cancelSearchResult() {
      this.searchResultsVisible = false;
      this.$message.info('已取消选择');
    },
    
    // 获取位置文本
    getLocationText(ad_info) {
      if (!ad_info) return '';
      const location = [ad_info.province, ad_info.city, ad_info.district]
        .filter(Boolean).join('');
      return location;
    }
  }
};
</script>

<style lang="scss" scoped>
.map-search-box {
  display: flex;
  align-items: center;
}
</style> 