function getRootPath(){
    var curWwwPath=window.document.location.href;
    var pathName=window.document.location.pathname;
    var pos=curWwwPath.indexOf(pathName);
    var localhostPath=curWwwPath.substring(0,pos);
    var projectName=pathName.substring(0,pathName.substr(1).indexOf('/')+1);
    window.rootPath=localhostPath+projectName;
    if(window.rootPath.indexOf("woyaotuanjian.com")<0){
      window.rootPath = 'http://localhost:8077/trip'
    }
}

//提取url参数
function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i"); 
    var r = window.location.search.substr(1).match(reg); 
    if (r != null) return unescape(r[2]); 
    return null; 
} 

//修改微信标题
function setTitle(title) {
    document.title = title;
    var i = document.createElement('iframe');
    i.src = '/favicon.ico';
    i.style.display = 'none';
    i.onload = function() {
      setTimeout(function(){
          i.remove();
      }, 9)
    }
    document.body.appendChild(i);
}

Date.prototype.format = function(fmt){ 
  var o = {   
    "M+" : this.getMonth()+1,                 //月份   
    "d+" : this.getDate(),                    //日   
    "h+" : this.getHours(),                   //小时   
    "m+" : this.getMinutes(),                 //分   
    "s+" : this.getSeconds(),                 //秒   
    "q+" : Math.floor((this.getMonth()+3)/3), //季度   
    "S"  : this.getMilliseconds()             //毫秒   
  };   
  if(/(y+)/.test(fmt))   
    fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));   
  for(var k in o)   
    if(new RegExp("("+ k +")").test(fmt))   
  fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));   
  return fmt;   
}

//设置微信jssdk相关
function setWxJs(share){
  //如果不是微信浏览器，直接返回
  var ua = navigator.userAgent.toLowerCase();
  if(ua.indexOf('micromessenger') < 0){
    return;
  }

  fetch(window.rootPath+`/weixin/jssdk?url=${encodeURIComponent(location.href)}`)
    .then(response => response.json())
    .then(json => {
      if(!json.success){
        console.log(json.message)
        return
      }
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: json.result.appId, // 必填，公众号的唯一标识
        timestamp: json.result.timestamp, // 必填，生成签名的时间戳
        nonceStr: json.result.noncestr, // 必填，生成签名的随机串
        signature: json.result.signature,// 必填，签名
        jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'] // 必填，需要使用的JS接口列表
      });
      //wxconfig是异步，会回调ready
      wx.ready(function(){
        //需在用户可能点击分享按钮前就先调用
        wx.updateAppMessageShareData({
          title: share.title, // 分享标题
          desc: share.desc, // 分享描述
          link: location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          imgUrl: share.imgUrl, // 分享图标
          success: function () {}
        });
        wx.updateTimelineShareData({
          title: share.title, // 分享标题
          link: location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          imgUrl: share.imgUrl, // 分享图标
          success: function () {}
        });
      });
    })
    .catch(err =>console.log("设置分享信息失败")); 
}

function report(params) {
  if (!params){
    params={}
  }
  if(!params.shopId){
    params.shopId=getQueryString("shopId")
  }
  if(!params.tripId){
    params.tripId=getQueryString("tripId")
  }
  if(!params.themeId){
    params.themeId=getQueryString("themeId")
  }
  if(!params.event){
    params.event="page"
  }
  if(!params.page){
    console.log(location.href)
    if(/.*\/(\w+\.html).*/.test(location.href+"")){
      params.page=RegExp.$1+""
    }else{
      params.page="/"
    }
  }
  let url=`${window.rootPath}/app/analysis/report`
  if (params) {
    let paramsArray = [];
    //拼接参数
    Object.keys(params).forEach(key => paramsArray.push(key + '=' + params[key]))
    url=url+"?"+paramsArray.join('&')
  }

  fetch(url,{
    headers:{
      "WYTJ-ANALYSIS-ID":localStorage.getItem("WYTJ-ANALYSIS-ID")||''
    }
    })
    .then(response => response.json())
    .then(json => {
      if(json.success){
        localStorage.setItem("WYTJ-ANALYSIS-ID",json.result["WYTJ-ANALYSIS-ID"])
      }
    })
    .catch(err =>console.log("report error"));
}

//启动统计分析库
getRootPath()
report({event:"page"})

//验证邮箱格式
function validateEmail(email) {
  // RFC 5322 标准的邮箱正则
  var regex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return regex.test(email);
}

//验证邮件发送频率限制
function validateEmailFrequency() {
  let nowTime = new Date().getTime();
  let lastEmailTime = localStorage.getItem('lastEmailTime')
  if(lastEmailTime && (nowTime-lastEmailTime < 30000)){
    return false;
  }
  return true;
}
