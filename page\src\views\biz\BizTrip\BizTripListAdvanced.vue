<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :xl="8">
            <a-form-item label="搜索">
              <a-input placeholder="编号/名称" @keyup.enter="searchQuery" v-model="queryParam.searchValue"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="4">
            <a :style="{ marginLeft: '4px', fontSize: '12px' }" @click="toggle"> {{ expand?'折叠':'更多' }} <a-icon :type="expand ? 'up' : 'down'" /> </a>
          </a-col>
           <a-col :xl="9">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">刷新</a-button>
              <a-button type="primary" v-has="`trip:add`" @click="openTab('biz-bizTrip-edit')" icon="plus" style="margin-left: 8px">新增</a-button>
            </span>
          </a-col>
        </a-row>
        <!-- 展开的搜索条件 -->
        <div v-show="expand">
          <a-row :gutter="24">
            <a-col :xl="5" :xxl="4">
              <a-form-item label="地区">
                <a-input placeholder="地区" v-model="queryParam.area"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="5" :xxl="4">
              <a-form-item label="景点">
                <a-input placeholder="景点" v-model="queryParam.scenic"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="5" :xxl="4">
              <a-form-item label="酒店">
                <a-input placeholder="酒店" v-model="queryParam.hotel"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="5" :xxl="4">
              <a-form-item label="餐厅">
                <a-input placeholder="餐厅" v-model="queryParam.rest"></a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :xl="5" :xxl="4">
              <a-form-item label="天数">
                <a-select placeholder="天数" v-model="queryParam.days">
                  <a-select-option value="0">不限</a-select-option>
                  <a-select-option value="1">1天</a-select-option>
                  <a-select-option value="2">2天</a-select-option>
                  <a-select-option value="3">3天</a-select-option>
                  <a-select-option value="4">4天</a-select-option>
                  <a-select-option value="5">5天</a-select-option>
                  <a-select-option value="6">6天</a-select-option>
                  <a-select-option value="7">7天+</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xl="5" :xxl="4">
              <a-form-item label="交通">
                <j-dict-select-tag
                  v-model="queryParam.traffic"
                  placeholder="全部"
                  dictCode="biz_traffic_type"
                />
              </a-form-item>
            </a-col>
            <a-col :xl="5" :xxl="4">
              <a-form-item label="主题">
                <j-dict-select-tag
                  v-model="queryParam.userTag"
                  placeholder="全部"
                  dictCode="biz_scenic_user_tag"
                />
              </a-form-item>
            </a-col>
            <a-col :xl="5" :xxl="4">
              <a-form-item label="特色">
                <a-select mode="multiple" placeholder="可多选" v-model="tripTagArr" @change="tripTagChange()">
                  <a-select-option v-for="(item,index) in this.searchPanelOption.tripTagList" :key="index" :value="item">
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :xl="5" :xxl="4">
              <a-form-item label="成本">
                <a-input-number v-model="queryParam.priceMin" :defaultValue="0" :min="0" :max="20000" :step="50" style="width:60px"/>~
                <a-input-number v-model="queryParam.priceMax" :defaultValue="5000" :min="0" :max="20000" :step="50" style="width:60px"/> 
             </a-form-item>
            </a-col>
            <a-col :xl="5" :xxl="4">
              <a-form-item label="状态">
                <j-dict-select-tag
                  v-model="queryParam.status"
                  placeholder="全部"
                  dictCode="biz_trip_status"
                />
              </a-form-item>
            </a-col>
            <a-col :xl="4" :xxl="4">
              <a-form-item label="作者">
                <a-input placeholder="作者" v-model="queryParam.sysUserName"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="5" :xxl="4" v-if="sysUser.roleCode.startsWith('shop')">
              <a-form-item label="范围" >
                <a-select default-value="1" v-model="queryParam.onlyCheckedCom" placeholder="已勾选专线">
                  <a-select-option value="1">已勾选专线</a-select-option>
                  <a-select-option value="2">全部专线</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xl="5" :xxl="4" v-if="sysUser.roleCode.startsWith('biz')">
              <a-form-item label="范围" >
                <a-select default-value="1" v-model="queryParam.onlyCheckedCom" placeholder="不含组团社行程">
                  <a-select-option value="1">不含组团社行程</a-select-option>
                  <a-select-option value="2">全部行程</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :xl="4" :xxl="4">
              <a-form-item label="类型">
                <a-select v-model='queryParam.type' placeholder="行程类型">
                  <a-select-option :value="''">
                    全部
                  </a-select-option>
                  <a-select-option :value="1">
                    团建行程
                  </a-select-option>
                  <a-select-option :value="2">
                    散拼行程
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xl="4" :xxl="4">
              <a-form-item label="备注">
                <a-input placeholder="备注内容" v-model="queryParam.remark"></a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
    </div>

    <!-- 高级表格区域 -->
    <div>
      <vxe-table
        ref="xTable"
        border
        stripe
        resizable
        highlight-hover-row
        highlight-current-row
        :data="dataSource"
        :loading="loading"
        :sort-config="{trigger: 'cell', remote: true}"
        :column-config="{resizable: true}"
        :scroll-x="{enabled: true, gt: 10}"
        :row-class-name="getVxeRowClassName"
        @sort-change="handleSortChange"
      >
        <!-- 编号列 -->
        <vxe-column 
          field="id" 
          title="编号" 
          width="80" 
          fixed="left"
          sortable
        ></vxe-column>
        
        <!-- 名称列 -->
        <vxe-column 
          field="tripName" 
          title="名称" 
          width="200"
          show-overflow="tooltip"
          :edit-render="{name: 'input', immediate: true}"
        ></vxe-column>
        
        <!-- 完全名称列 -->
        <vxe-column 
          field="tripFullName" 
          title="完全名称" 
          min-width="300"
          show-overflow="tooltip"
        ></vxe-column>
        
        <!-- 流量统计列 -->
        <vxe-column 
          title="流量(PV|PDF|WORD)" 
          width="180"
          align="center"
        >
          <template #default="{ row }">
            <span>{{ row.pv }} | {{ row.pdf }} | {{ row.word }}</span>
          </template>
        </vxe-column>
        
        <!-- 推荐指数列 -->
        <vxe-column 
          field="searchIndex" 
          title="推荐指数" 
          width="100"
          sortable
          align="center"
        ></vxe-column>
        
        <!-- 状态列 -->
        <vxe-column 
          field="status_dictText" 
          title="状态" 
          width="80"
          align="center"
        >
          <template #default="{ row }">
            <a-tag :color="getStatusColor(row.status)">
              {{ row.status_dictText }}
            </a-tag>
          </template>
        </vxe-column>
        
        <!-- 备注列 -->
        <vxe-column 
          field="remark" 
          title="备注" 
          width="200"
          show-overflow="tooltip"
        >
          <template #default="{ row }">
                          <div 
                class="remark-cell" 
                :class="{
                  'remark-editable': sysUser.roleCode === 'admin' || sysUser.roleCode.startsWith('biz') || sysUser.id === row.sysUserId,
                  'remark-readonly': !(sysUser.roleCode === 'admin' || sysUser.roleCode.startsWith('biz') || sysUser.id === row.sysUserId)
                }"
                @click.stop.prevent="editRemark(row)"
                @mousedown.stop
                @mouseup.stop
                :title="sysUser.roleCode === 'admin' || sysUser.roleCode.startsWith('biz') || sysUser.id === row.sysUserId ? '点击编辑' : '无编辑权限'"
              >
              <span v-if="row.remark" class="remark-content">{{ row.remark }}</span>
              <span v-else-if="sysUser.roleCode === 'admin' || sysUser.roleCode.startsWith('biz') || sysUser.id === row.sysUserId" class="remark-placeholder">点击编辑...</span>
              <span v-else class="remark-readonly-text">无备注</span>
            </div>
          </template>
        </vxe-column>
        
        <!-- 作者列 -->
        <vxe-column 
          field="sysUserName" 
          title="作者" 
          width="120"
          show-overflow="tooltip"
        >
          <template #default="{ row }">
            <span v-if="sysUser.roleCode=='admin'||sysUser.roleCode.startsWith('biz')||sysUser.id==row.sysUserId">
              {{ row.sysUserName }}
            </span>
            <span v-if="sysUser.roleCode.startsWith('shop')&&sysUser.id!=row.sysUserId">
              {{ row.companyName }}
            </span>
          </template>
        </vxe-column>
        
        <!-- 操作列 -->
        <vxe-column 
          title="操作" 
          width="120" 
          fixed="right"
          align="center"
        >
          <template #default="{ row }">
            <a-dropdown :trigger="['hover']" :mouseLeaveDelay="0.8" :mouseEnterDelay="0.1" placement="bottomRight">
              <a-button type="primary" size="small" style="border-radius: 6px;">
                操作 <a-icon type="down" />
              </a-button>
              <a-menu slot="overlay" class="operation-menu">
                <a-menu-item v-if="row.type !== 2" class="menu-item-with-icon">
                  <a @click="preview(row)">
                    <a-icon type="eye" style="margin-right: 8px; color: #1890ff;" />
                    预览
                  </a>
                </a-menu-item>     
                <a-menu-item v-if="sysUser.roleCode=='admin'||sysUser.roleCode.startsWith('biz')||sysUser.id==row.sysUserId" class="menu-item-with-icon">
                  <a @click="openTab('biz-bizTrip-edit',row)">
                    <a-icon type="edit" style="margin-right: 8px; color: #52c41a;" />
                    编辑
                  </a>
                </a-menu-item>  
                <a-menu-item class="menu-item-with-icon">
                  <a @click="exportWord(row)" v-if="row.type !== 2">
                    <a-icon type="download" style="margin-right: 8px; color: #722ed1;" />
                    导出
                  </a>
                </a-menu-item>     
                     
                <a-menu-divider />
                <a-menu-item v-has="`trip:add`" class="menu-item-with-icon">
                  <a-popconfirm title="确定复制吗?" @confirm="() => copy(row)" placement="left">
                    <a>
                      <a-icon type="copy" style="margin-right: 8px; color: #13c2c2;" />
                      复制
                    </a>
                  </a-popconfirm>
                </a-menu-item>
                <a-menu-item v-if="sysUser.roleCode=='admin'||sysUser.roleCode.startsWith('biz')" class="menu-item-with-icon">
                  <a @click="share(row)">
                    <a-icon type="share-alt" style="margin-right: 8px; color: #eb2f96;" />
                    分享
                  </a>
                </a-menu-item>
                <a-menu-item class="menu-item-with-icon">
                  <a @click="showTripLink(row)">
                    <a-icon type="link" style="margin-right: 8px; color: #2f54eb;" />
                    链接
                  </a>
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item v-if="sysUser.roleCode=='admin'||sysUser.roleCode.startsWith('biz')||sysUser.id==row.sysUserId" class="menu-item-danger">
                  <a-popconfirm :title="`确定删除 [${row.id}] ${row.tripName} | ${row.tripFullName}吗?`" @confirm="() => handleDelete(row.id)" placement="left">
                    <a style="color: #ff4d4f;">
                      <a-icon type="delete" style="margin-right: 8px;" />
                      删除
                    </a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </template>
        </vxe-column>
      </vxe-table>
      
      <!-- 分页组件 -->
      <div style="margin-top: 16px; text-align: right;">
        <a-pagination
          :current="ipagination.current"
          :total="ipagination.total"
          :pageSize="ipagination.pageSize"
          :showSizeChanger="true"
          :showQuickJumper="true"
          :showTotal="(total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条`"
          @change="handleTableChange"
          @showSizeChange="handleTableChange"
        />
      </div>
    </div>

    <!-- 表单区域保持不变 -->
    <ShareTripModal ref="shareModal" @ok="modalFormOk" @shareOk="shareOk"></ShareTripModal>
    <ShowTripLinkModal ref="showTripLink" @ok="modalFormOk"></ShowTripLinkModal>
    <ExportWordModal ref="exportWordModal" @ok="modalFormOk"></ExportWordModal>

    <!-- 其他模态框保持不变 -->
    <a-modal title="行程保存成功" :width="500" :visible="showNewTripModal" :footer="null" @cancel="showNewTripModal=false" cancelText="关闭">
      <a-form>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="行程编号">
          {{ record.id }}
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="行程名称">
          {{ record.tripName }}
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="快捷操作">
            <a-button block @click="preview(record)" v-if="record.type !== 2">预览</a-button>
            <a-button block @click="showTripLink(record)">二维码</a-button>
            <a-button block @click="exportWord(record)" v-if="record.type !== 2">导出文件</a-button>
            <a-button v-if="sysUser.id === 1048" block @click="editAiContent(record)">编辑发布小程序/H5</a-button>
            <a-button v-if="(sysUser.roleCode==='admin'||sysUser.roleCode.startsWith('biz')) && record.type !== 2"
                      block @click="share(record)">分享</a-button>
        </a-form-item>          
      </a-form>
    </a-modal>

    <!-- 备注编辑模态框 -->
    <a-modal
      title="编辑备注"
      :visible="remarkModalVisible"
      @ok="saveRemark"
      @cancel="cancelRemark"
      :confirmLoading="remarkLoading"
      okText="保存"
      cancelText="取消"
      :width="700"
      :bodyStyle="{ padding: '24px' }"
    >
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="备注内容">
          <a-textarea
            v-model="remarkForm.remark"
            placeholder="请输入备注内容，支持换行..."
            :autoSize="{ minRows: 8, maxRows: 20 }"
            :maxLength="1000"
            showCount
            style="resize: vertical; font-size: 14px; line-height: 1.6;"
          />
          <div style="margin-top: 8px; color: #666; font-size: 12px;">
            💡 提示：您可以输入多行文本，最多1000个字符
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

  </a-card>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixinCopy'
import { RouterLinkMixinConfig } from '@/mixins/RouterLinkMixinConfig';
import { httpAction, getAction } from '@/api/manage'
import ShareTripModal from './modules/ShareTripModal'
import ShowTripLinkModal from './modules/ShowTripLinkModal'
import ExportWordModal from './modules/ExportWordModal'

export default {
  name: "BizTripListAdvanced",
  mixins: [JeecgListMixin, RouterLinkMixinConfig],
  components: {
    ShareTripModal,
    ShowTripLinkModal,
    ExportWordModal,
  },
  data() {
    return {
      description: '行程管理页面（高级表格版）',
      sysUser: {},
      tripTagArr: [],
      searchPanelOption: {},
      expand: false,
      url: {
        list: "/biz/bizTrip/list",
        delete: "/biz/bizTrip/delete",
        deleteBatch: "/biz/bizTrip/deleteBatch",
        exportXlsUrl: "biz/bizTrip/exportXls",
        importExcelUrl: "biz/bizTrip/importExcel",
        copy: "biz/bizTrip/copy",
        exportWord: "biz/bizTrip/exportFile",
        getCurrentShop: "biz/bizShop/getCurrentShop",
      },
      showNewTripModal: false,
      record: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      remarkModalVisible: false,
      remarkForm: {
        id: '',
        remark: ''
      },
      remarkLoading: false,
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  created() {
    this.sysUser = this.$store.getters.userInfo
  },
  mounted() {
    this.fetchPanel();
  },
  activated() {
    console.log("trip list active")
    this.checkNewTripModal()
  },
  methods: {
    toggle() {
      this.expand = !this.expand;
    },
    
    // 新增的表格事件处理方法
    handleSortChange({ column, property, order }) {
      console.log('排序变化:', { column, property, order })
      // 这里可以实现远程排序
      if (order) {
        this.queryParam.column = property
        this.queryParam.order = order === 'asc' ? 'asc' : 'desc'
      } else {
        delete this.queryParam.column
        delete this.queryParam.order
      }
      this.loadData()
    },
    


    getStatusColor(status) {
      const colorMap = {
        '1': 'green',
        '2': 'orange', 
        '3': 'red',
        '0': 'default'
      }
      return colorMap[status] || 'default'
    },
    
    // 获取VXE表格行样式类名
    getVxeRowClassName({ row }) {
      if (row.type === 2) {
        return 'sp-row-style'
      }
      return ''
    },
    
    // 编辑备注
    editRemark(row) {
      console.log('点击编辑备注:', row)
      console.log('当前用户权限:', this.sysUser)
      
      // 检查权限
      if (!(this.sysUser.roleCode === 'admin' || 
            this.sysUser.roleCode.startsWith('biz') || 
            this.sysUser.id === row.sysUserId)) {
        this.$message.warning('您没有权限编辑此备注')
        return
      }
      
      this.remarkForm.id = row.id
      this.remarkForm.remark = row.remark || ''
      this.remarkModalVisible = true
      
      // 确保模态框能正确显示
      this.$nextTick(() => {
        console.log('模态框状态:', this.remarkModalVisible)
      })
    },
    
    // 保存备注
    saveRemark() {
      this.remarkLoading = true
      const params = new URLSearchParams()
      params.append('id', this.remarkForm.id)
      params.append('remark', this.remarkForm.remark || '')
      
      httpAction('/biz/bizTrip/updateRemark', params, 'post').then((res) => {
        if (res.success) {
          this.$message.success('备注更新成功')
          this.remarkModalVisible = false
          // 更新本地数据
          const row = this.dataSource.find(item => item.id === this.remarkForm.id)
          if (row) {
            row.remark = this.remarkForm.remark || ''
          }
        } else {
          this.$message.error(res.message || '备注更新失败')
        }
      }).catch((error) => {
        this.$message.error('备注更新失败：' + error.message)
      }).finally(() => {
        this.remarkLoading = false
      })
    },
    
    // 取消编辑备注
    cancelRemark() {
      this.remarkModalVisible = false
      this.remarkForm = {
        id: '',
        remark: ''
      }
    },


    
    // 获取表格实例
    getTableInstance() {
      return this.$refs.xTable
    },
    
    // 刷新表格
    refreshTable() {
      this.$refs.xTable.refreshData()
    },
    

    
    // 导出表格数据
    exportTableData() {
      this.$refs.xTable.exportData({
        filename: '行程数据',
        type: 'xlsx'
      })
    },
    
    // 原有方法保持不变
    copy(record) {
      httpAction(this.url.copy, record, "post").then((res) => {
        if (res.success) {
          this.$message.success(res.message);
          this.openTab('biz-bizTrip-edit', res.result)
        } else {
          this.$message.warning(res.message);
        }
      }).finally(() => {})
    },
    
    share(record) {
      this.showNewTripModal = false
      this.$refs.shareModal.show(record)
    },
    
    showTripLink(record) {
      this.showNewTripModal = false
      this.$refs.showTripLink.show(record)
    },
    
    shareOk(record) {
      this.showTripLink(record)
    },
    
    async preview(record){
      this.showNewTripModal=false
      
      // 获取公司配置
      let companyType = 2; // 默认为平铺图文（周边）
      try {
        const { getCompanyConfig } = await import('@/utils/companyUtil');
        const companyConfig = await getCompanyConfig(this.sysUser.comId);
        if (companyConfig) {
          companyType = companyConfig.companyType || 2;
        }
      } catch (error) {
        console.error('获取公司配置失败', error);
      }

      // 根据公司类型选择预览方式
      if (companyType === 1) {
        // 折叠景点（长线）类型 - 检查是否有AI内容
        this.checkAiContentAndPreview(record);
      } else {
        // 平铺图文（周边）类型 - 使用原有预览方式
        this.previewTraditionalH5(record);
      }
    },

    // 检查AI内容并预览
    checkAiContentAndPreview(record) {
      getAction(`/biz/bizTrip/hasAiContent`, { tripId: record.id })
        .then((res) => {
          if (res.success && res.result) {
            // 有AI内容，预览新版H5
            this.previewNewH5(record);
          } else {
            // 没有AI内容，提示错误
            this.$message.warning('没有预览，需要先创建 小程序/H5');
          }
        })
        .catch(error => {
          console.error('检查AI内容时发生错误:', error);
          // 出错时也提示需要创建
          this.$message.warning('没有预览，需要先创建 小程序/H5');
        });
    },

    // 预览新版H5
    previewNewH5(record) {
      //获取当前店铺信息
      httpAction(this.url.getCurrentShop,{},"get").then((res)=>{
        if(res.success){
          const url = `https://www.woyaotuanjian.com/uni/#/pages/trip/TripDetail?tripId=${record.code}&shopId=${res.result.code}`;
          this.openPreviewWindow(url, '新版H5预览');
        }else{//没有关联店铺
          this.$message.warning(res.message);
        }
      }).finally(() => {

      })
    },

    // 预览传统H5
    previewTraditionalH5(record) {
      //适用于单独启动h5页面
      // let prefix="http://127.0.0.1"

      //适用于随管理后台启动h5页面
      let prefix="h5/"
      if(window.location.href.toString().indexOf("woyaotuanjian.com")>-1){//发布到线上
        prefix="https://www.woyaotuanjian.com/trip/h5"
      }
      let url=`${prefix}/tripDetail.html?preview=true`
      //获取当前店铺信息
      httpAction(this.url.getCurrentShop,{},"get").then((res)=>{
        if(res.success){
          url=url+"&shopId="+res.result.code+"&tripId="+record.code
          this.openPreviewWindow(url, '行程预览');
        }else{//没有关联店铺
          this.$message.warning(res.message);
        }
      }).finally(() => {

      })
    },

    // 统一的预览窗口打开方法
    openPreviewWindow(url, title = '预览') {
      // 计算最佳预览窗口尺寸
      const screenWidth = window.screen.availWidth;
      const screenHeight = window.screen.availHeight;
      
      // 移动端预览窗口尺寸 (iPhone 12 Pro Max 比例)
      const previewWidth = 428;
      const previewHeight = Math.min(926, screenHeight - 100);
      
      // 居中计算
      const left = Math.max(0, (screenWidth - previewWidth) / 2);
      const top = Math.max(0, (screenHeight - previewHeight) / 2);
      
      // 窗口特性配置
      const features = [
        `width=${previewWidth}`,
        `height=${previewHeight}`,
        `left=${left}`,
        `top=${top}`,
        'toolbar=no',
        'menubar=no',
        'scrollbars=yes', // 允许滚动
        'resizable=yes',  // 允许调整大小
        'location=no',
        'status=no',
        'titlebar=yes'    // 显示标题栏
      ].join(',');
      
      // 打开预览窗口
      const previewWindow = window.open(url, '_blank', features);
      
      // 设置窗口标题（如果可能）
      if (previewWindow) {
        previewWindow.document.title = title;
        
        // 窗口加载完成后的处理
        previewWindow.addEventListener('load', () => {
          // 可以在这里添加一些预览窗口的样式优化
          try {
            const style = previewWindow.document.createElement('style');
            style.textContent = `
              body {
                margin: 0;
                padding: 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              }
              /* 添加移动端视口样式 */
              @media (max-width: 480px) {
                body {
                  font-size: 14px;
                  line-height: 1.5;
                }
              }
            `;
            previewWindow.document.head.appendChild(style);
          } catch (e) {
            // 跨域限制时忽略样式注入
            console.log('无法注入预览样式，可能存在跨域限制');
          }
        });
        
        // 聚焦到预览窗口
        previewWindow.focus();
        
        // 显示成功提示
        this.$message.success(`${title}窗口已打开`);
      } else {
        // 窗口被阻止时的提示
        this.$message.warning('预览窗口被浏览器阻止，请允许弹窗后重试');
      }
    },
    
    exportWord(record) {
      this.showNewTripModal = false
      this.$refs.exportWordModal.show(record)
    },
    
    fetchPanel() {
      let url = `/biz/bizTrip/searchPanel`
      getAction(url)
        .then(res => {
          if (res.success) {
            this.searchPanelOption = res.result;
          } else {
            this.$message.warning(res.message || '请求错误');
          }
        })
        .catch(res => {
          this.$message.warning(res.message || '网络错误');
        });
    },
    
    tripTagChange() {
      this.queryParam.tripTagStr = this.tripTagArr.join(',')
    },
    
    editAiContent(record) {
      this.showNewTripModal = false
      this.$router.push({
        name: 'biz-bizTrip-aiContent',
        query: { id: record.id }
      })
    },
    

    
    checkNewTripModal() {
      let newTrip = sessionStorage.getItem("newTrip")
      if (newTrip) {
        sessionStorage.removeItem("newTrip")
        this.showNewTripModal = true
        console.log("newTrip", newTrip)
        getAction(this.url.list, { id: newTrip }).then(res => {
          if (res.success) {
            if (res.result.records) {
              this.record = res.result.records[0]
            }
          }
        });
      }
    }
  }
}
</script>

<style scoped>
@import '~@assets/less/common.less';

/* 自定义表格样式 - 与 BizTripList.vue 保持一致 */
.vxe-table {
  font-size: 14px;
}

.vxe-table .vxe-header--column {
  background-color: #fafafa;
  font-weight: 500;
}

.vxe-table .vxe-body--row {
  height: 54px; /* 与 ant-table middle 尺寸保持一致 */
}

.vxe-table .vxe-body--row:hover {
  background-color: #e6f7ff;
}

.vxe-table .vxe-body--row:nth-child(even) {
  background-color: #fafafa; /* 斑马纹效果 */
}

.vxe-table .vxe-body--row:nth-child(even):hover {
  background-color: #e6f7ff;
}

/* 选中行样式 */
.vxe-table .vxe-body--row.row--checked {
  background-color: #f0f9ff;
}

/* 可编辑单元格样式 */
.vxe-table .vxe-body--column.col--edit {
  background-color: #fff7e6;
}

/* 散拼行程样式 - 与 BizTripList.vue 保持一致 */
.vxe-table .vxe-body--row.sp-row-style {
  background-color: #fff7e6 !important;
}

.vxe-table .vxe-body--row.sp-row-style:hover {
  background-color: #fff2e6 !important;
}

/* 备注列编辑样式 */
.remark-cell {
  cursor: pointer;
  padding: 8px;
  min-height: 40px;
  display: flex;
  align-items: center;
  width: 100%;
  border-radius: 4px;
  transition: all 0.3s;
  position: relative;
  z-index: 1;
}

.remark-cell:hover {
  background-color: #f0f9ff;
  border: 1px dashed #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.remark-cell:active {
  background-color: #e6f7ff;
  transform: scale(0.98);
}

/* 备注内容样式 */
.remark-content {
  color: #333;
  line-height: 1.4;
  word-break: break-word;
  flex: 1;
  pointer-events: none;
}

.remark-placeholder {
  color: #bfbfbf;
  font-style: italic;
  font-size: 12px;
  flex: 1;
  pointer-events: none;
}

.remark-placeholder::after {
  content: " ✏️";
  opacity: 0;
  transition: opacity 0.3s;
  margin-left: 4px;
}

.remark-cell:hover .remark-placeholder::after {
  opacity: 1;
}

.remark-cell:hover .remark-content::after {
  content: " ✏️";
  color: #1890ff;
  opacity: 0.7;
  margin-left: 4px;
}

/* 可编辑和只读状态样式 */
.remark-editable {
  cursor: pointer;
}

.remark-readonly {
  cursor: default;
  opacity: 0.6;
}

.remark-readonly:hover {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.remark-readonly-text {
  color: #999;
  font-style: italic;
  font-size: 12px;
  flex: 1;
  pointer-events: none;
}

/* 备注编辑器样式 */
.vxe-table .vxe-body--column .vxe-textarea {
  border: 2px solid #1890ff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  resize: vertical;
  width: 100%;
  min-height: 60px;
  padding: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.vxe-table .vxe-body--column .vxe-textarea:focus {
  border-color: #40a9ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  outline: none;
}

/* 编辑状态的单元格样式 */
.vxe-table .vxe-body--column.col--edit {
  background-color: #f0f9ff;
  border: 1px solid #1890ff;
}

/* 操作菜单样式 */
.operation-menu {
  min-width: 160px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
}

.operation-menu .ant-dropdown-menu-item {
  padding: 10px 16px;
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.3s;
}

.operation-menu .menu-item-with-icon {
  display: flex;
  align-items: center;
}

.operation-menu .menu-item-with-icon a {
  display: flex;
  align-items: center;
  width: 100%;
  color: #333;
  text-decoration: none;
  font-weight: 500;
}

.operation-menu .ant-dropdown-menu-item:hover {
  background-color: #f5f5f5;
  transform: translateX(2px);
}

.operation-menu .menu-item-danger:hover {
  background-color: #fff2f0;
}

.operation-menu .menu-item-danger:hover a {
  color: #ff4d4f !important;
}

.operation-menu .ant-dropdown-menu-item-divider {
  margin: 8px 0;
  background-color: #f0f0f0;
}

/* 操作按钮样式 */
.vxe-table .vxe-body--column .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
  transition: all 0.3s;
}

.vxe-table .vxe-body--column .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}
</style> 