<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="素材搜索">
              <a-input placeholder="素材名称" @keyup.enter="searchQuery" v-model="queryParam.materialName"></a-input>
            </a-form-item>
          </a-col>
          
          <a-col :md="6" :sm="8">
            <a-form-item label="素材类型">
              <a-select placeholder="请选择类型" v-model="queryParam.materialType" allowClear>
                <a-select-option value="1">图片素材</a-select-option>
                <a-select-option value="2">富文本素材</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :md="6" :sm="8">
            <a-form-item label="标签分类">
              <a-input placeholder="标签" v-model="queryParam.tags"></a-input>
            </a-form-item>
          </a-col>

          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">刷新</a-button>
              <a-button type="primary" @click="handleAdd" icon="plus" style="margin-left: 8px">新增素材</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange">

        <span slot="materialType" slot-scope="text">
          <a-tag :color="text === '1' ? 'blue' : 'green'">
            {{ text === '1' ? '图片素材' : '富文本素材' }}
          </a-tag>
        </span>

        <span slot="thumbnail" slot-scope="text, record">
          <div v-if="record.materialType === '1'" style="text-align: center;">
            <img :src="text" style="max-width: 60px; max-height: 60px; border-radius: 4px;" />
          </div>
          <div v-else style="text-align: center;">
            <a-icon type="file-text" style="font-size: 24px; color: #1890ff;" />
          </div>
        </span>

        <span slot="tags" slot-scope="text">
          <a-tag v-for="tag in (text || '').split(',')" :key="tag" v-if="tag.trim()">
            {{ tag.trim() }}
          </a-tag>
        </span>

        <span slot="content" slot-scope="text, record">
          <div v-if="record.materialType === '2'" style="max-width: 200px;">
            <div v-html="text" style="
              max-height: 60px; 
              overflow: hidden; 
              font-size: 12px; 
              line-height: 1.2;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
              display: -webkit-box;
            "></div>
          </div>
          <span v-else>-</span>
        </span>

        <span slot="action" slot-scope="text, record">
          <a-dropdown>
            <a class="ant-dropdown-link">操作 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">查看</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定复制吗?" @confirm="() => copy(record)">
                  <a>复制</a>
                </a-popconfirm>
              </a-menu-item>
              <template v-if="sysUser.roleCode=='admin'||((sysUser.roleCode=='biz' || sysUser.roleCode=='bizSuper')&&(record.comId != 0))||sysUser.id==record.sysUserId">
                <a-menu-item>
                  <a @click="handleEdit(record)">编辑</a>
                </a-menu-item>
                <a-menu-item>
                  <a @click="showMaterialLink(record)">二维码</a>
                </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a>删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </template>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 帮助说明区域 -->
    <div class="help-section">
      <a-alert
        message="使用帮助"
        type="info"
        show-icon
        closable
      >
        <template slot="description">
          <div class="help-content">
            <p>素材库是将常见的图片和富文本保存</p>
            <p>可用于WORD封面图或者小程序H5定制师推荐语等场景</p>
            <p>图片素材：单张图片</p>
            <p>富文本素材：多图文组成的段落</p>
          </div>
        </template>
      </a-alert>
    </div>

    <!-- 表单区域 -->
    <biz-material-modal ref="modalForm" @ok="modalFormOk"></biz-material-modal>
    
    <!-- 二维码弹窗 -->
    <show-material-link-modal ref="showMaterialLinkModal"></show-material-link-modal>
  </a-card>
</template>

<script>
  import BizMaterialModal from './modules/BizMaterialModal'
  import ShowMaterialLinkModal from './modules/ShowMaterialLinkModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixinCopy'
  import { httpAction } from '@/api/manage'

  export default {
    name: "BizMaterialList",
    mixins: [JeecgListMixin],
    components: {
      BizMaterialModal,
      ShowMaterialLinkModal
    },
    data() {
      return {
        description: '图文素材库管理页面',
        sysUser: {},

        // 表头
        columns: [
          {
            title: '#',
            align: "center",
            dataIndex: 'id',
            width: 80
          },
          {
            title: '素材名称',
            align: "center",
            dataIndex: 'materialName',
            width: 150
          },
          {
            title: '素材类型',
            align: "center",
            dataIndex: 'materialType',
            scopedSlots: { customRender: 'materialType' },
            width: 120
          },
          {
            title: '预览',
            align: "center",
            dataIndex: 'thumbnail',
            scopedSlots: { customRender: 'thumbnail' },
            width: 100
          },
          {
            title: '内容预览',
            align: "center",
            dataIndex: 'content',
            scopedSlots: { customRender: 'content' },
            width: 220
          },
          {
            title: '标签',
            align: "center",
            dataIndex: 'tags',
            scopedSlots: { customRender: 'tags' },
            width: 150
          },
          {
            title: '创建时间',
            align: "center",
            dataIndex: 'createTime',
            width: 150
          },
          {
            title: '作者',
            align: "center",
            dataIndex: 'createBy',
            width: 100
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: "center",
            scopedSlots: { customRender: 'action' },
            width: 120
          }
        ],
        url: {
          list: "/biz/bizMaterial/list",
          copy: "/biz/bizMaterial/copy",
          delete: "/biz/bizMaterial/delete",
          deleteBatch: "/biz/bizMaterial/deleteBatch",
          exportXlsUrl: "biz/bizMaterial/exportXls",
          importExcelUrl: "biz/bizMaterial/importExcel",
        },
      }
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },
    mounted() {
      this.sysUser = this.$store.getters.userInfo
    },
    methods: {
      copy(record) {
        httpAction(this.url.copy, record, "post").then((res) => {
          if (res.success) {
            this.$message.success(res.message);
            this.handleEdit(res.result)
          } else {
            this.$message.warning(res.message);
          }
        }).finally(() => {

        })
      },
      handleDetail(record) {
        this.$refs.modalForm.detail(record);
      },
      showMaterialLink(record) {
        this.$refs.showMaterialLinkModal.show(record);
      }
    }
  }
</script>

<style scoped>
  @import '~@assets/less/common.less';
  
  .help-section {
    margin-top: 20px;
  }
  
  .help-content p {
    margin: 4px 0;
    color: #666;
    font-size: 14px;
  }
</style> 