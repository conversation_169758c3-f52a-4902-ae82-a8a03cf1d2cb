package com.woyaotuanjian.modules.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.woyaotuanjian.modules.biz.entity.BizTripRoute;
import com.woyaotuanjian.modules.biz.mapper.BizTripRouteMapper;
import com.woyaotuanjian.modules.biz.service.IBizTripRouteService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Description: 行程路线配置
 */
@Service
public class BizTripRouteServiceImpl extends ServiceImpl<BizTripRouteMapper, BizTripRoute> implements IBizTripRouteService {

    @Override
    public List<BizTripRoute> getByTripId(Long tripId) {
        return baseMapper.selectByTripId(tripId);
    }

    @Override
    public BizTripRoute getByRoute(Long tripId, Long originId, String originType, 
                                  Long destinationId, String destinationType) {
        return baseMapper.selectByRoute(tripId, originId, originType, destinationId, destinationType);
    }

    @Override
    public boolean saveOrUpdateRoute(BizTripRoute route) {
        // 查询是否已存在相同的路线配置
        BizTripRoute existing = getByRoute(route.getTripId(), route.getOriginId(), route.getOriginType(),
                                          route.getDestinationId(), route.getDestinationType());
        
        Date now = new Date();
        if (existing != null) {
            // 更新现有记录
            existing.setTransportType(route.getTransportType());
            existing.setDuration(route.getDuration());
            existing.setDistance(route.getDistance());
            existing.setUpdateTime(now);
            existing.setUpdateBy(route.getUpdateBy());
            return updateById(existing);
        } else {
            // 创建新记录
            route.setCreateTime(now);
            route.setUpdateTime(now);
            return save(route);
        }
    }

    @Override
    public boolean deleteByTripId(Long tripId) {
        QueryWrapper<BizTripRoute> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("trip_id", tripId);
        return remove(queryWrapper);
    }
} 