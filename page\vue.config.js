const path = require('path');
// const UglifyJsPlugin = require('uglifyjs-webpack-plugin');

function resolve(dir) {
  return path.join(__dirname, dir);
}

// vue.config.js
module.exports = {
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  //基本路径
  publicPath: './',
  // 输出文件目录
  outputDir: process.env.VUE_APP_OUT_DIR,


  chainWebpack: config => {
    // 配置别名
    config.resolve.alias
      .set('@$', resolve('src'))
      .set('@api', resolve('src/api'))
      .set('@assets', resolve('src/assets'))
      .set('@comp', resolve('src/components'))
      .set('@views', resolve('src/views'))
      .set('@layout', resolve('src/layout'))
      .set('@static', resolve('src/static'));

    config.plugin('define').tap(args => {
      const pkg = require('./package.json');
      args[0]['process.env'].VUE_APP_VERSION = JSON.stringify(pkg.version);
      return args;
    });
  },

  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          modifyVars: {
            /* less 变量覆盖，用于自定义 ant design 主题 */
            'primary-color': '#13C2C2',
            'link-color': '#13C2C2',
            'border-radius-base': '4px',
            'heading-color': 'rgba(0, 0, 0, 1)', // 标题色
            'text-color': 'rgba(0, 0, 0, 1)' // 主文本色
          },
          javascriptEnabled: true
        }
      },
      postcss: {
        plugins: [
          require('autoprefixer')
        ]
      }
    }
  },

  devServer: {
    port: 3300,
    proxy: {
      '/': {
        target: 'http://127.0.0.1:8077/trip', //请求本地后台项目
        ws: false,
        changeOrigin: true
      }
    }
  },

  lintOnSave: undefined
};
