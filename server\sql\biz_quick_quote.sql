-- 快捷报价表
CREATE TABLE `biz_quick_quote` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `quote_name` varchar(255) DEFAULT NULL COMMENT '报价名称',
  `quote_details` longtext COMMENT '报价明细（JSON格式）',
  `source_type` varchar(50) DEFAULT NULL COMMENT '来源类型（paste/trip/file）',
  `source_content` longtext COMMENT '原始来源内容',
  `total_amount` decimal(10,2) DEFAULT NULL COMMENT '总金额',
  `per_person_amount` decimal(10,2) DEFAULT NULL COMMENT '人均金额',
  `person_count` int(11) DEFAULT NULL COMMENT '人数',
  `service_fee_type` varchar(20) DEFAULT NULL COMMENT '服务费类型（percentage/fixed）',
  `service_fee_value` decimal(10,2) DEFAULT NULL COMMENT '服务费值',
  `service_fee_name` varchar(100) DEFAULT NULL COMMENT '服务费名称',
  `final_remark` text DEFAULT NULL COMMENT '最后备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`),
  KEY `idx_quote_name` (`quote_name`),
  KEY `idx_source_type` (`source_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='快捷报价表'; 