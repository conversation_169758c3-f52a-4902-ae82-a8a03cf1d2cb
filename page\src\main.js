// import 'babel-polyfill'
import Vue from 'vue';
import App from './App.vue';
import Storage from 'vue-ls';
import router from './router';
import store from './store/';

import { VueAxios } from '@/utils/request';

import Antd from 'ant-design-vue';
import Viser from 'viser-vue';
import 'ant-design-vue/dist/antd.less'; // or 'ant-design-vue/dist/antd.less'

import '@/permission'; // permission control
import '@/utils/filter'; // base filter
import Print from 'vue-print-nb-jeecg';
import VueApexCharts from 'vue-apexcharts';

import preview from 'vue-photo-preview';
import 'vue-photo-preview/dist/skin.css';
import VueClipboard from 'vue-clipboard2';

// 引入 Vxe Table
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';

import TlbsMap from 'tlbs-map-vue';

import {
  ACCESS_TOKEN,
  DEFAULT_COLOR,
  DEFAULT_THEME,
  DEFAULT_LAYOUT_MODE,
  DEFAULT_COLOR_WEAK,
  SIDEBAR_TYPE,
  DEFAULT_FIXED_HEADER,
  DEFAULT_FIXED_HEADER_HIDDEN,
  DEFAULT_FIXED_SIDEMENU,
  DEFAULT_CONTENT_WIDTH_TYPE,
  DEFAULT_MULTI_PAGE
} from '@/store/mutation-types';
import config from '@/defaultSettings';

import JDictSelectTag from './components/dict/index.js';
import { hasPermission, dragPermission, focusPermission } from '@/utils/hasPermission';
import vueBus from '@/utils/vueBus';
import './assets/iconfont/iconfont.css';

Vue.config.productionTip = false;
Vue.use(hasPermission);
Vue.use(dragPermission);
Vue.use(focusPermission);
Vue.use(Storage, config.storageOptions);
Vue.use(Antd);
Vue.use(VueAxios, router);
Vue.use(Viser);
Vue.use(JDictSelectTag);
Vue.use(Print);
Vue.use(VueApexCharts);
Vue.component('apexchart', VueApexCharts);
Vue.use(preview);
Vue.use(vueBus);
Vue.use(VueClipboard);
Vue.use(VXETable);
// 注册 TlbsMap (Vue 2.7 内置了 Composition API)
Vue.use(TlbsMap)


Vue.prototype.$message.config({
  top: '280px',
  duration: 2,
  maxCount: 3
});
//线上清除console.log
if (process.env.NODE_ENV === 'production') {
  window.console.log = function() {};
}

// 添加版本检测
const VERSION = process.env.VUE_APP_VERSION; // 从环境变量获取版本号

// 版本检测函数
const checkVersion = () => {
  const lastVersion = localStorage.getItem('app_version');
  
  // 只在存在旧版本号，且版本号不一致时自动刷新
  if (lastVersion && lastVersion !== VERSION) {
    localStorage.setItem('app_version', VERSION);
    window.location.reload();
  } else {
    // 如果是新用户(没有版本记录)，直接记录当前版本号
    localStorage.setItem('app_version', VERSION);
  }
};

// 在 Vue 实例创建前检查版本
checkVersion();

new Vue({
  router,
  store,
  mounted() {
    store.commit('SET_SIDEBAR_TYPE', Vue.ls.get(SIDEBAR_TYPE, true));
    store.commit('TOGGLE_THEME', Vue.ls.get(DEFAULT_THEME, config.navTheme));
    store.commit('TOGGLE_LAYOUT_MODE', Vue.ls.get(DEFAULT_LAYOUT_MODE, config.layout));
    store.commit('TOGGLE_FIXED_HEADER', Vue.ls.get(DEFAULT_FIXED_HEADER, config.fixedHeader));
    store.commit('TOGGLE_FIXED_SIDERBAR', Vue.ls.get(DEFAULT_FIXED_SIDEMENU, config.fixSiderbar));
    store.commit('TOGGLE_CONTENT_WIDTH', Vue.ls.get(DEFAULT_CONTENT_WIDTH_TYPE, config.contentWidth));
    store.commit('TOGGLE_FIXED_HEADER_HIDDEN', Vue.ls.get(DEFAULT_FIXED_HEADER_HIDDEN, config.autoHideHeader));
    store.commit('TOGGLE_WEAK', Vue.ls.get(DEFAULT_COLOR_WEAK, config.colorWeak));
    store.commit('TOGGLE_COLOR', Vue.ls.get(DEFAULT_COLOR, config.primaryColor));
    store.commit('SET_TOKEN', Vue.ls.get(ACCESS_TOKEN));
    store.commit('SET_MULTI_PAGE', Vue.ls.get(DEFAULT_MULTI_PAGE, true));
  },
  render: h => h(App)
}).$mount('#app');
