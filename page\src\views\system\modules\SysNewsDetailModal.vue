<template>
  <a-modal
    title="动态详情"
    :width="800"
    :visible="visible"
    :footer="null"
    @cancel="handleCancel"
  >
    <div v-if="model.id">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="标题" :span="2">
          <strong>{{ model.title }}</strong>
          <a-tag 
            v-if="model.isTop === 1" 
            color="red" 
            style="margin-left: 8px"
          >
            置顶
          </a-tag>
        </a-descriptions-item>
        
        <a-descriptions-item label="类型">
          <a-tag :color="model.type === 'notice' ? 'blue' : 'green'">
            {{ model.type === 'notice' ? '通知' : '版本更新' }}
          </a-tag>
        </a-descriptions-item>
        
        <a-descriptions-item label="状态">
          <a-tag :color="model.status === 1 ? 'green' : 'red'">
            {{ model.status === 1 ? '启用' : '禁用' }}
          </a-tag>
        </a-descriptions-item>
        
        <a-descriptions-item label="创建人">
          {{ model.createBy }}
        </a-descriptions-item>
        
        <a-descriptions-item label="创建时间">
          {{ model.createTime }}
        </a-descriptions-item>
        
        <a-descriptions-item label="可见角色" :span="2">
          <template v-if="model.visibleRoles">
            <a-tag 
              v-for="role in visibleRoleList" 
              :key="role"
              color="blue"
              style="margin-bottom: 4px;"
            >
              {{ getRoleName(role) }}
            </a-tag>
          </template>
          <span v-else style="color: #999;">所有角色可见</span>
        </a-descriptions-item>
      </a-descriptions>
      
      <a-divider />
      
      <div class="content-section">
        <h4>内容</h4>
        <div 
          class="content-html" 
          v-html="model.content"
        ></div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { getAction } from '@/api/manage';

export default {
  name: 'SysNewsDetailModal',
  data() {
    return {
      visible: false,
      model: {},
      roleList: []
    };
  },
  computed: {
    visibleRoleList() {
      if (!this.model.visibleRoles) return [];
      return this.model.visibleRoles.split(',').filter(role => role.trim());
    }
  },
  created() {
    this.loadRoleList();
  },
  methods: {
    show(record) {
      this.model = Object.assign({}, record);
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    handleCancel() {
      this.close();
    },
    loadRoleList() {
      getAction('/sys/news/getRoleList').then(res => {
        if (res.success) {
          this.roleList = res.result;
        }
      });
    },
    getRoleName(roleCode) {
      const role = this.roleList.find(r => r.roleCode === roleCode);
      return role ? role.roleName : roleCode;
    }
  }
};
</script>

<style scoped>
.content-section {
  margin-top: 16px;
}

.content-html {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fafafa;
  min-height: 200px;
}

.content-html >>> img {
  max-width: 100%;
  height: auto;
}

.content-html >>> p {
  margin-bottom: 12px;
}

.content-html >>> ul, .content-html >>> ol {
  padding-left: 24px;
}
</style> 