package com.woyaotuanjian.modules.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.common.constant.RoleConstant;
import com.woyaotuanjian.common.system.query.QueryGenerator;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import com.woyaotuanjian.modules.system.entity.SysNews;
import com.woyaotuanjian.modules.system.entity.SysRole;
import com.woyaotuanjian.modules.system.service.ISysNewsService;
import com.woyaotuanjian.modules.system.service.ISysRoleService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 系统动态 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("/sys/news")
public class SysNewsController {

    @Autowired
    private ISysNewsService sysNewsService;
    
    @Autowired
    private ISysRoleService sysRoleService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "系统动态-分页列表查询")
    @ApiOperation(value="系统动态-分页列表查询", notes="系统动态-分页列表查询")
    @GetMapping(value = "/list")
    @RequiresRoles(logical = Logical.OR, value = {RoleConstant.ADMIN, RoleConstant.B1SUPER})
    public Result queryPageList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                HttpServletRequest req) {
        Result result = new Result();
        QueryWrapper<SysNews> queryWrapper = QueryGenerator.initQueryWrapper(new SysNews(), req.getParameterMap());
        queryWrapper.orderByDesc("is_top").orderByDesc("create_time");
        
        Page<SysNews> page = new Page<SysNews>(pageNo, pageSize);
        IPage<SysNews> pageList = sysNewsService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "系统动态-添加")
    @ApiOperation(value="系统动态-添加", notes="系统动态-添加")
    @PostMapping(value = "/add")
    @RequiresRoles(logical = Logical.OR, value = {RoleConstant.ADMIN, RoleConstant.B1SUPER})
    public Result<SysNews> add(@RequestBody JSONObject jsonObject) {
        Result<SysNews> result = new Result<SysNews>();
        try {
            SysNews sysNews = JSON.parseObject(jsonObject.toJSONString(), SysNews.class);
            LoginUser loginUser = SysUserUtil.getCurrentUser();
            sysNews.setCreateBy(loginUser.getUsername());
            sysNews.setCreateTime(new Date());
            sysNews.setUpdateTime(new Date());
            if (sysNews.getStatus() == null) {
                sysNews.setStatus(1); // 默认启用
            }
            if (sysNews.getIsTop() == null) {
                sysNews.setIsTop(0); // 默认不置顶
            }
            sysNewsService.save(sysNews);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑
     */
    @AutoLog(value = "系统动态-编辑")
    @ApiOperation(value="系统动态-编辑", notes="系统动态-编辑")
    @PutMapping(value = "/edit")
    @RequiresRoles(logical = Logical.OR, value = {RoleConstant.ADMIN, RoleConstant.B1SUPER})
    public Result<SysNews> edit(@RequestBody JSONObject jsonObject) {
        Result<SysNews> result = new Result<SysNews>();
        try {
            SysNews sysNews = JSON.parseObject(jsonObject.toJSONString(), SysNews.class);
            SysNews sysNewsEntity = sysNewsService.getById(sysNews.getId());
            if (sysNewsEntity == null) {
                result.error500("未找到对应实体");
            } else {
                LoginUser loginUser = SysUserUtil.getCurrentUser();
                sysNews.setUpdateBy(loginUser.getUsername());
                sysNews.setUpdateTime(new Date());
                boolean ok = sysNewsService.updateById(sysNews);
                if (ok) {
                    result.success("修改成功!");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "系统动态-通过id删除")
    @ApiOperation(value="系统动态-通过id删除", notes="系统动态-通过id删除")
    @DeleteMapping(value = "/delete")
    @RequiresRoles(logical = Logical.OR, value = {RoleConstant.ADMIN, RoleConstant.B1SUPER})
    public Result delete(@RequestParam(name="id",required=true) String id) {
        Result result = new Result();
        SysNews sysNews = sysNewsService.getById(id);
        if (sysNews == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = sysNewsService.removeById(id);
            if (ok) {
                result.success("删除成功!");
            }
        }
        return result;
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "系统动态-批量删除")
    @ApiOperation(value="系统动态-批量删除", notes="系统动态-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @RequiresRoles(logical = Logical.OR, value = {RoleConstant.ADMIN, RoleConstant.B1SUPER})
    public Result<SysNews> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        Result<SysNews> result = new Result<SysNews>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            this.sysNewsService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "系统动态-通过id查询")
    @ApiOperation(value="系统动态-通过id查询", notes="系统动态-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<SysNews> queryById(@RequestParam(name="id",required=true) String id) {
        Result<SysNews> result = new Result<SysNews>();
        SysNews sysNews = sysNewsService.getById(id);
        if (sysNews == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(sysNews);
            result.setSuccess(true);
        }
        return result;
    }

    /**
     * 获取当前用户可见的动态列表
     */
    @AutoLog(value = "系统动态-获取可见动态")
    @ApiOperation(value="系统动态-获取可见动态", notes="系统动态-获取可见动态")
    @GetMapping(value = "/getVisibleNews")
    public Result<List<SysNews>> getVisibleNews() {
        Result<List<SysNews>> result = new Result<>();
        try {
            LoginUser loginUser = SysUserUtil.getCurrentUser();
            String roleCode = loginUser != null ? loginUser.getRoleCode() : null;
            List<SysNews> newsList = sysNewsService.getVisibleNewsByRole(roleCode);
            result.setResult(newsList);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("获取动态失败");
        }
        return result;
    }

    /**
     * 获取所有角色列表
     */
    @AutoLog(value = "系统动态-获取角色列表")
    @ApiOperation(value="系统动态-获取角色列表", notes="系统动态-获取角色列表")
    @GetMapping(value = "/getRoleList")
    @RequiresRoles(logical = Logical.OR, value = {RoleConstant.ADMIN, RoleConstant.B1SUPER})
    public Result<List<SysRole>> getRoleList() {
        Result<List<SysRole>> result = new Result<>();
        try {
            List<SysRole> roleList = sysRoleService.list();
            result.setResult(roleList);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("获取角色列表失败");
        }
        return result;
    }
} 