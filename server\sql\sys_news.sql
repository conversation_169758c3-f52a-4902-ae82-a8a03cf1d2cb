-- 系统动态表
CREATE TABLE `sys_news` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` longtext COMMENT '内容',
  `type` varchar(20) NOT NULL COMMENT '类型：notice-通知，version-版本更新',
  `visible_roles` varchar(500) DEFAULT NULL COMMENT '可见角色列表，逗号分隔，为空表示所有角色可见',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶：0-否，1-是',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_is_top` (`is_top`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统动态表';

-- 插入示例数据
INSERT INTO `sys_news` (`title`, `content`, `type`, `visible_roles`, `is_top`, `status`, `create_by`, `create_time`, `update_time`) VALUES
('欢迎使用系统动态功能', '<p>系统动态功能已正式上线！</p><p><strong>主要功能：</strong></p><ul><li>支持发布通知和版本更新</li><li>支持角色权限控制</li><li>支持置顶功能</li><li>支持富文本编辑</li></ul><p>如有任何问题，请联系管理员。</p>', 'notice', '', 1, 1, 'admin', NOW(), NOW()),
('系统优化更新 v1.0.1', '<p>本次更新内容：</p><ul><li>优化了系统性能</li><li>修复了已知问题</li><li>新增了动态管理功能</li><li>改进了用户体验</li></ul><p><strong>注意事项：</strong></p><p>请及时更新到最新版本以获得更好的使用体验。</p>', 'version', '', 0, 1, 'admin', NOW(), NOW()); 