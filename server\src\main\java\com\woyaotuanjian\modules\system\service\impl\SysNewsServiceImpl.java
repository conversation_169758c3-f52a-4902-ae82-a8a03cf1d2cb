package com.woyaotuanjian.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.woyaotuanjian.modules.system.entity.SysNews;
import com.woyaotuanjian.modules.system.mapper.SysNewsMapper;
import com.woyaotuanjian.modules.system.service.ISysNewsService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 系统动态 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class SysNewsServiceImpl extends ServiceImpl<SysNewsMapper, SysNews> implements ISysNewsService {

    @Override
    public List<SysNews> getVisibleNewsByRole(String roleCode) {
        LambdaQueryWrapper<SysNews> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysNews::getStatus, 1); // 只查询启用状态的动态
        
        // 如果有角色编码，添加角色权限过滤
        if (StringUtils.hasText(roleCode)) {
            queryWrapper.and(wrapper -> 
                // 使用 FIND_IN_SET 函数精确匹配逗号分隔的角色列表
                wrapper.apply("FIND_IN_SET({0}, visible_roles) > 0", roleCode)
                       .or()
                       .isNull(SysNews::getVisibleRoles)
                       .or()
                       .eq(SysNews::getVisibleRoles, "")
            );
        }
        
        // 按置顶和创建时间排序
        queryWrapper.orderByDesc(SysNews::getIsTop)
                   .orderByDesc(SysNews::getCreateTime);
        
        return this.list(queryWrapper);
    }
    
} 