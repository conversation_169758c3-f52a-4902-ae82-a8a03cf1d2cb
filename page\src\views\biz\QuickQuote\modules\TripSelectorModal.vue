<template>
  <a-modal
    title="选择行程"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="loading"
    width="800px"
    :okButtonProps="{ disabled: !selectedTrip }"
  >
    <!-- 搜索框 -->
    <div class="search-section">
      <a-input-search
        v-model="searchKeyword"
        placeholder="搜索行程ID或名称"
        @search="handleSearch"
        @pressEnter="handleSearch"
        style="margin-bottom: 16px"
      />
    </div>

    <!-- 行程列表 -->
    <div class="trip-list">
      <a-radio-group v-model="selectedTrip" style="width: 100%">
        <div v-if="trips.length === 0 && !searchLoading" class="empty-state">
          <a-empty description="暂无行程数据" />
        </div>
        
        <a-spin :spinning="searchLoading">
          <div 
            v-for="trip in trips" 
            :key="trip.id" 
            class="trip-item"
            :class="{ 'trip-item-selected': selectedTrip === trip.id }"
            @click="selectedTrip = trip.id"
          >
            <a-radio :value="trip.id" style="width: 100%">
              <div class="trip-content">
                <div class="trip-header">
                  <span class="trip-name">{{ trip.tripName }}</span>
                  <span class="trip-id">ID: {{ trip.id }}</span>
                </div>
                <div class="trip-meta">
                  <span v-if="trip.dayNum" class="trip-days">{{ trip.dayNum }}天行程</span>
                  <span class="trip-date">{{ formatDate(trip.createTime) }}</span>
                </div>
                <div v-if="trip.advantageDesc" class="trip-desc">
                  {{ trip.advantageDesc }}
                </div>
              </div>
            </a-radio>
          </div>
        </a-spin>
      </a-radio-group>
    </div>

    <!-- 分页 -->
    <div v-if="total > pageSize" class="pagination-section">
      <a-pagination
        v-model="currentPage"
        :total="total"
        :pageSize="pageSize"
        :showSizeChanger="false"
        :showQuickJumper="true"
        @change="handlePageChange"
        size="small"
      />
    </div>
  </a-modal>
</template>

<script>
import { getAction } from '@/api/manage'
import moment from 'moment'

export default {
  name: 'TripSelectorModal',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      trips: [],
      selectedTrip: null,
      searchKeyword: '',
      searchLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        this.initModal()
      } else {
        this.resetModal()
      }
    }
  },

  methods: {
    // 初始化模态框
    async initModal() {
      this.selectedTrip = null
      this.searchKeyword = ''
      this.currentPage = 1
      
      // 加载最新的行程列表
      await this.loadRecentTrips()
    },

    // 重置模态框
    resetModal() {
      this.trips = []
      this.selectedTrip = null
      this.searchKeyword = ''
      this.currentPage = 1
      this.total = 0
    },

    // 加载最新行程
    async loadRecentTrips() {
      try {
        this.searchLoading = true
        const response = await getAction('/biz/bizQuickQuote/getRecentTrips')
        
        if (response.success) {
          this.trips = response.result || []
          this.total = this.trips.length
        } else {
          this.$message.error('加载行程失败')
        }
      } catch (error) {
        console.error('加载行程失败:', error)
        this.$message.error('加载行程失败')
      } finally {
        this.searchLoading = false
      }
    },

    // 搜索行程
    async handleSearch() {
      this.currentPage = 1
      await this.loadTrips()
    },

    // 加载行程列表
    async loadTrips() {
      try {
        this.searchLoading = true
        const params = {
          pageNo: this.currentPage,
          pageSize: this.pageSize
        }
        
        if (this.searchKeyword.trim()) {
          params.keyword = this.searchKeyword.trim()
        }
        
        const response = await getAction('/biz/bizQuickQuote/searchTrips', params)
        
        if (response.success) {
          const pageData = response.result
          this.trips = pageData.records || []
          this.total = pageData.total || 0
        } else {
          this.$message.error('搜索失败')
        }
      } catch (error) {
        console.error('搜索失败:', error)
        this.$message.error('搜索失败')
      } finally {
        this.searchLoading = false
      }
    },

    // 分页变化
    handlePageChange(page) {
      this.currentPage = page
      this.loadTrips()
    },

    // 确定选择
    handleOk() {
      if (!this.selectedTrip) {
        this.$message.error('请选择一个行程')
        return
      }
      
      this.$emit('ok', this.selectedTrip)
    },

    // 取消选择
    handleCancel() {
      this.$emit('cancel')
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return moment(date).format('YYYY-MM-DD')
    }
  }
}
</script>

<style lang="less" scoped>
.search-section {
  margin-bottom: 16px;
}

.trip-list {
  max-height: 400px;
  overflow-y: auto;
  
  .empty-state {
    text-align: center;
    padding: 40px 0;
  }

  .trip-item {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin-bottom: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }

    &.trip-item-selected {
      border-color: #1890ff;
      background-color: #f6ffed;
    }

    .trip-content {
      width: 100%;
      
      .trip-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .trip-name {
          font-size: 16px;
          font-weight: 500;
          color: #262626;
        }

        .trip-id {
          font-size: 12px;
          color: #8c8c8c;
          background: #f5f5f5;
          padding: 2px 6px;
          border-radius: 2px;
        }
      }

      .trip-meta {
        display: flex;
        gap: 16px;
        margin-bottom: 8px;
        font-size: 12px;
        color: #8c8c8c;

        .trip-days {
          color: #1890ff;
        }
      }

      .trip-desc {
        font-size: 12px;
        color: #595959;
        line-height: 1.4;
        max-height: 32px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }
}

.pagination-section {
  margin-top: 16px;
  text-align: center;
}

// 移动端适配
@media (max-width: 768px) {
  .trip-item {
    .trip-content {
      .trip-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }

      .trip-meta {
        flex-direction: column;
        gap: 4px;
      }
    }
  }
}
</style> 