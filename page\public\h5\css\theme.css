@font-face {
  font-family: 'DOUYU-Font';
  src: url('./font/DOUYUFont.ttf') format('truetype');
}

#app {
  position: relative;
  background-color: #9EDA9E;
}


.head-img-box {
  position: relative;
  margin-bottom: .5rem;
}

.head-img {
  width: 100%;
  height: 20rem;
  object-fit: cover;
}

.head-bg {
  position: absolute;
  bottom: 0;
  width: 100%;
  z-index: 100;
}

.theme-title-box {
  width: 100%;
  position: absolute;
  top: 8rem;
}

.theme-title {
  background: #99DD67;
  border: white solid .1rem;
  border-radius: 34px 34px 34px 34px;
  line-height: 2rem;
  font-size: 1rem;
  padding: .2rem 1.5rem 0;
  max-width: 20rem;
  font-family: DOUYU-Font;
  font-weight: normal;
  color: white;
}

.square-img {
  width: 100%;
  height: 0;
  overflow: hidden;
  padding-bottom: 100%;
  position: relative;
  border-radius: 0.5rem;
}

.square-img img {
  width: 100%;
  height: 100%;
  position: absolute;
  object-fit: cover;
  border-radius: 0.5rem;
}

.card {
  background-color: #fff;
  width: 96%;
  margin: .5rem auto;

  border-radius: 0.5rem;
  padding: .5rem;
  box-sizing: border-box;
}

.tag-content {
  width: 100%;
  display: -webkit-flex; /* Safari */
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.trip-tag {
  border-radius: 0.2rem;
  margin-right: 0.5rem;
  margin-bottom: 0.2rem;
}

.trip-title {
  color: #333333;
  font-weight: 700;
  font-size: 1rem;
  margin: 0.5rem 0;
}

.trip-full-title {
  color: #333333;
  font-weight: 400;
  font-size: 0.8rem;
  margin: 0.5rem 0;
}

.trip-row {
  width: 100%;
  display: -webkit-flex; /* Safari */
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  height: 1.5rem;
}

.trip-price {
  text-align: right;
  color: #FF6C01;
  font-weight: 900;
  font-size: 1rem;
  width: 100%;
}

.trip-price::before {
  content: '￥ ';
  font-size: 0.8rem;
}

.trip-price::after {
  content: ' 起';
  font-size: 0.8rem;
  color: #898786;
  font-weight: 400;
}

.trip-id {
  color: #333333;
  font-weight: 400;
  font-size: 0.8rem;
  width: 40%;
  margin: auto 0;
}

.trip-id::before {
  content: '编号:';
}

.locationBtn {
  width: 100%;
  height: 100%;
  padding: 10px 12px;
  line-height: 34px;
  font-weight: 700;
  color: #0185FF;
  position: relative;
}

.van-search__action {
  font-weight: 700;
  color: #0185FF;
  font-size: 16px;
}

.van-icon-location-o {
  font-weight: 700;
  color: #0185FF;
  font-size: 16px;
}

.search-tag {
  margin-right: 14px;
  margin-bottom: 10px;
  font-size: 14px;
  padding: 4px 12px;
}

.drop-down-panel {
  padding: 0 16px;
  position: relative;
}

.drop-down-title {
  margin-bottom: 10px;
  font-size: 14px;
}

.search-tag-container {
  display: -webkit-flex; /* Safari */
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  /* justify-content:space-around; */
}

.search-tag-container:last-child {
  margin-bottom: 3rem;
  /* 最后一个标签不被按钮挡住 */
}

.scroll-panel {
  max-height: 50vh;
  overflow-y: scroll;
}

.drop-down-btn-group {
  width: 100%;
  border-top: 1px solid #f1f1f1;
  padding: 10px 0;
}

.sticky-btn-group {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2003;
  background-color: #fff;
}

.drop-down-btn {
  width: 49%;
  display: inline-block;
  text-align: center;
  color: #333333;
}

.van-icon-location-o {
  position: relative;
  top: 2px;
}

.custom-button {
  width: 1.5rem;
  height: 1.5rem;
  background-color: #0185FF;
  border-radius: 50%;
}

.van-dropdown-menu__bar {
  box-shadow: none;
}

.shop-btn {
  width: 4rem;
  height: 4rem;
  background-color: #0185FF;
  font-size: 0.2rem;
  color: #fff;
  border-radius: 50%;
  position: fixed;
  right: 2rem;;
  bottom: 3rem;
  z-index: 2;
  text-align: center;
}

.shop-tag-container {
  width: 96%;
  height: 1.5rem;
  margin: 0 auto;
  display: block;
  overflow-x: scroll;
  white-space: nowrap;
}

.shop-tag-container > .search-tag {
  height: 1rem;
  display: inline-block;
  margin-bottom: 0;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  background-color: transparent;
}

#loading {
  width: 100vw;
  height: 100vh;
  text-align: center;
  background-color: #fff;
  color: #0185FF;
  position: absolute;
  z-index: 9999;
}

#loading > div {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}