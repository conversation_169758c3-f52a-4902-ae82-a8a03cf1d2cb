package com.woyaotuanjian.modules.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.woyaotuanjian.modules.biz.entity.BizMaterial;
import com.woyaotuanjian.modules.biz.mapper.BizMaterialMapper;
import com.woyaotuanjian.modules.biz.service.IBizMaterialService;
import org.apache.shiro.SecurityUtils;
import com.woyaotuanjian.common.system.vo.LoginUser;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Description: 图文素材库
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Service
public class BizMaterialServiceImpl extends ServiceImpl<BizMaterialMapper, BizMaterial> implements IBizMaterialService {

    @Override
    public BizMaterial copyMaterial(BizMaterial original) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        // 创建新的素材对象
        BizMaterial copy = new BizMaterial();
        
        // 复制基本信息
        copy.setMaterialName(original.getMaterialName() + "_副本");
        copy.setMaterialType(original.getMaterialType());
        copy.setContent(original.getContent());
        copy.setThumbnail(original.getThumbnail());
        copy.setTags(original.getTags());
        copy.setStatus(1); // 默认启用
        
        // 设置权限相关字段
        copy.setComId(original.getComId());
        copy.setSysOrgCode(original.getSysOrgCode());
        
        // 设置创建信息
        copy.setCreateBy(sysUser.getUsername());
        copy.setCreateTime(new Date());
        copy.setSysUserId(sysUser.getId().longValue());
        copy.setSysUserName(sysUser.getRealname());
        
        // 保存到数据库（ID由数据库自动生成）
        this.save(copy);
        
        return copy;
    }
} 