<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woyaotuanjian.modules.biz.mapper.BizTripRouteMapper">

    <!-- 根据行程ID获取所有路线配置 -->
    <select id="selectByTripId" resultType="com.woyaotuanjian.modules.biz.entity.BizTripRoute">
        SELECT * FROM biz_trip_route 
        WHERE trip_id = #{tripId}
        ORDER BY create_time ASC
    </select>
    
    <!-- 根据起止点查询路线配置 -->
    <select id="selectByRoute" resultType="com.woyaotuanjian.modules.biz.entity.BizTripRoute" parameterType="map">
        SELECT * FROM biz_trip_route 
        WHERE trip_id = #{tripId}
          AND origin_id = #{originId}
          AND origin_type = #{originType}
          AND destination_id = #{destinationId}
          AND destination_type = #{destinationType}
        LIMIT 1
    </select>

</mapper> 