body{
  background-color: #fafbff;

}
#app{
  position: relative;
  box-sizing: border-box;
}

#loading{
  width: 100vw;
  height: 100vh;
  text-align: center;
  background-color: #fff;
  color:#0185FF;
  position: absolute;
  z-index: 9999;
}
#loading > div{
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}
.container{
  margin: 0;
  padding: 0.6rem;
  display: block;
  position: relative;
}
.card{
  margin-bottom: 1rem;
  background-color: #fff;
  box-sizing: border-box;
  box-shadow: 0 0.5rem 0.5rem 0 #f1f4fe; 
}

.custom-button {
  width: 1.5rem;
  height: 1.5rem;
  background-color: #0185FF;
  border-radius: 50%;
}
.slider-container{
  padding: 0rem 1.5rem 0.5rem 1.5rem;
}
.van-cell::after{
  border-bottom: 0;
}
.tips{
  font-size: 0.8rem;
  color: #666;
  padding: 0.5rem 1.5rem;
}
.day-title{
  font-size: 1rem;
  margin: 0.8rem 0 0.4rem 0;
  color:#969799;
}
.hotel-container{
  padding: 0rem 1.5rem 1.5rem 1.5rem;
}
.hotel-container .hotel-name{
  font-size: 0.8rem;
}
.card .van-cell__left-icon::before,.van-cell__title{
  color: #0185FF;
}
.van-checkbox__label{
  margin-left: 2px;
}
.finalPrice{
  font-size: 1.5rem;
  color: #FF6C01;
}
.finalPrice::after{
  content: '';
  font-size: 1.5rem;
  color: #FF6C01;
}
.debug-info{
  color:#f00;
  font-size: 0.8rem;
}