<template>
  <div class="region-filter">
    <!-- 第一行：地区筛选和系统景区选项 -->
    <div class="filter-row">
      <div class="filter-left">
        <a-cascader
          v-model="currentSelection"
          :options="areaData"
          :show-search="true"
          :change-on-select="true"
          placeholder="地区筛选"
          @change="onRegionSelect"
          :allow-clear="true"
          :field-names="{ label: 'label', value: 'value', children: 'children' }"
          :expand-trigger="'hover'"
          size="small"
        />
      </div>
      
      <div class="divider"></div>
      
      <div class="filter-right">
        <a-checkbox 
          v-model="systemScenicDataChecked" 
          @change="onSystemScenicChange"
          size="small"
        >
          系统景区
        </a-checkbox>
        <a-button 
          type="link" 
          size="small" 
          @click="clearAll" 
          v-if="selectedRegions.length > 0"
          class="clear-btn"
        >
          清空
        </a-button>
      </div>
    </div>
    
    <!-- 第二行：已选择的地区标签或提示信息 -->
    <div class="selected-regions-row" v-if="selectedRegions && selectedRegions.length > 0">
      <div class="selected-regions">
        <a-tag
          v-for="(region, index) in selectedRegions"
          :key="`region-${index}-${region.label}`"
          closable
          @close="removeRegion(index)"
          class="region-tag"
        >
          {{ region.label }}
        </a-tag>
      </div>
    </div>
    
    <!-- 系统景区数据需要地区筛选的提示 -->
    <div class="selected-regions-row warning-text" v-else-if="showWarning">
      <span class="warning-message">
        <i class="warning-icon">⚠</i>
        使用系统景区时，必须进行地区筛选
      </span>
    </div>
  </div>
</template>

<script>
import ChineseDistricts from '@/assets/distpicker.data.min.js';

// 转换地区数据格式的函数
function getCityArr(cityJson) {
  let arr = [];
  // 只处理第一级数据（省份）
  if (cityJson[1]) {
    for (let item in cityJson[1]) {
            const provinceCode = convertCode(item);
      const provinceName = cityJson[1][item];
      
      // 跳过全国选项
      if (provinceName === '全国') continue;

      arr.push({
        value: provinceCode,
        label: provinceName,
        children: getCityArrChild(cityJson, provinceCode)
      });
    }
  }
  return arr;
}

function getCityArrChild(cityJson, provinceCode) {
  let Carr = [];
  if (cityJson[provinceCode]) {
    for (let item in cityJson[provinceCode]) {
      const cityCode = item;
      const cityName = cityJson[provinceCode][item];
      
      Carr.push({ 
        value: cityCode, 
        label: cityName, 
        children: getAreaArrChild(cityJson, cityCode) 
      });
    }
  }
  return Carr;
}

function getAreaArrChild(cityJson, cityCode) {
  let Aarr = [];
  if (cityJson[cityCode]) {
    for (let item in cityJson[cityCode]) {
      const areaCode = item;
      const areaName = cityJson[cityCode][item];
      
      Aarr.push({ 
        value: areaCode, 
        label: areaName 
      });
    }
  }
  return Aarr;
}

// 转换科学计数法代码为标准代码
function convertCode(code) {
  if (typeof code === 'string' && code.includes('e')) {
    return String(Number(code));
  }
  return String(code);
}

export default {
  name: 'RegionFilter',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    systemScenicData: {
      type: Boolean,
      default: false
    },
    // 是否启用本地存储，默认启用
    enableStorage: {
      type: Boolean,
      default: true
    },
    // 存储键名，可自定义
    storageKey: {
      type: String,
      default: 'region-filter-data'
    }
  },
  data() {
    return {
      areaData: [],
      currentSelection: [],
      selectedRegions: [],
      systemScenicDataChecked: false,
      showWarning: false
    };
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && Array.isArray(newVal)) {
          this.selectedRegions = [...newVal];
        }
      },
      immediate: true
    },
    systemScenicData: {
      handler(newVal) {
        this.systemScenicDataChecked = newVal;
      },
      immediate: true
    }
  },
  created() {
    // 在组件创建时就加载存储的数据
    this.loadFromStorage();
  },
  mounted() {
    this.initAreaData();
    // 确保在mounted后再次触发事件，以防父组件错过了created阶段的事件
    this.$nextTick(() => {
      this.forceEmitInitialData();
    });
  },
  methods: {
    initAreaData() {
      this.areaData = getCityArr(ChineseDistricts);
    },
    
    onRegionSelect(value, selectedOptions) {
      if (!value || value.length === 0) {
        return;
      }
      
      // 构建地区标签
      const label = selectedOptions.map(option => option.label).join(' / ');
      
      // 检查是否已经选择过这个地区
      const exists = this.selectedRegions.some(region => 
        JSON.stringify(region.value) === JSON.stringify(value)
      );
      
      if (!exists) {
        const newRegion = {
          value: value,
          label: label,
          // 保存各级地区名称，用于后端筛选
          province: selectedOptions[0] ? selectedOptions[0].label : '',
          city: selectedOptions[1] ? selectedOptions[1].label : '',
          area: selectedOptions[2] ? selectedOptions[2].label : ''
        };
        
        // 使用Vue.set确保响应式更新
        this.$set(this.selectedRegions, this.selectedRegions.length, newRegion);
        
        // 手动触发事件
        this.emitChange();
        
        // 保存到本地存储
        this.saveToStorage();
        
        // 如果添加了地区且有警告提示，隐藏提示
        if (this.showWarning) {
          this.showWarning = false;
        }
      }
      
      // 清空当前选择
      this.$nextTick(() => {
        this.currentSelection = [];
      });
    },
    
    removeRegion(index) {
      this.selectedRegions.splice(index, 1);
      // 如果勾选了系统景区数据但没有地区了，取消勾选
      if (this.systemScenicDataChecked && this.selectedRegions.length === 0) {
        this.systemScenicDataChecked = false;
        this.onSystemScenicChange(false);
      }
      this.emitChange();
      // 保存到本地存储
      this.saveToStorage();
    },
    
    clearAll() {
      this.selectedRegions = [];
      this.currentSelection = [];
      // 隐藏警告提示
      this.showWarning = false;
      // 如果勾选了系统景区数据，取消勾选
      if (this.systemScenicDataChecked) {
        this.systemScenicDataChecked = false;
        this.onSystemScenicChange(false);
      }
      this.emitChange();
      // 清理本地存储
      this.clearStorage();
    },
    
    onSystemScenicChange(checked) {
      // 处理 Ant Design Vue checkbox 的 change 事件参数
      // checked 可能是事件对象或布尔值
      const isChecked = typeof checked === 'boolean' ? checked : checked.target.checked;
      
      // 如果勾选系统景区数据但没有选择地区，阻止勾选并显示提示
      if (isChecked && this.selectedRegions.length === 0) {
        this.$nextTick(() => {
          this.systemScenicDataChecked = false;
        });
        this.showWarning = true;
        // 3秒后隐藏提示
        setTimeout(() => {
          this.showWarning = false;
        }, 3000);
        return;
      }
      
      // 隐藏提示
      this.showWarning = false;
      this.$emit('system-scenic-change', isChecked);
      // 保存到本地存储
      this.saveToStorage();
    },
    
    emitChange() {
      this.$emit('input', this.selectedRegions);
      this.$emit('change', this.selectedRegions);
    },
    
    // 获取选中的地区数据，供父组件调用
    getSelectedRegions() {
      return this.selectedRegions;
    },
    
    // 强制触发初始化事件，供父组件在需要时调用
    forceEmitInitialData() {
      if (this.selectedRegions.length > 0) {
        this.emitChange();
      }
      if (this.systemScenicDataChecked) {
        this.$emit('system-scenic-change', this.systemScenicDataChecked);
      }
    },
    
    // 保存到本地存储
    saveToStorage() {
      if (!this.enableStorage) return;
      
      try {
        const data = {
          selectedRegions: this.selectedRegions,
          systemScenicDataChecked: this.systemScenicDataChecked,
          timestamp: Date.now()
        };
        localStorage.setItem(this.storageKey, JSON.stringify(data));
      } catch (error) {
        console.warn('保存地区筛选数据到本地存储失败:', error);
      }
    },
    
    // 从本地存储加载
    loadFromStorage() {
      if (!this.enableStorage) return;
      
      try {
        const stored = localStorage.getItem(this.storageKey);
        if (stored) {
          const data = JSON.parse(stored);
          
          // 数据永不过期，直接恢复数据
          
          // 恢复选中的地区
          if (data.selectedRegions && Array.isArray(data.selectedRegions)) {
            this.selectedRegions = data.selectedRegions;
          }
          
          // 恢复系统景区选择状态
          if (typeof data.systemScenicDataChecked === 'boolean') {
            this.systemScenicDataChecked = data.systemScenicDataChecked;
          }
          
          // 确保数据恢复后立即触发事件通知父组件
          this.$nextTick(() => {
            if (this.selectedRegions.length > 0) {
              this.emitChange();
            }
            if (this.systemScenicDataChecked) {
              this.$emit('system-scenic-change', this.systemScenicDataChecked);
            }
          });
        }
      } catch (error) {
        console.warn('从本地存储加载地区筛选数据失败:', error);
        this.clearStorage();
      }
    },
    
    // 清理本地存储
    clearStorage() {
      if (!this.enableStorage) return;
      
      try {
        localStorage.removeItem(this.storageKey);
      } catch (error) {
        console.warn('清理地区筛选本地存储失败:', error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.region-filter {
  background: #fafbfc;
  border-radius: 8px;
  padding: 6px;
  margin-bottom: 8px;
  border: 1px solid #e8eaec;
  
  .filter-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 0px;
    flex-wrap: nowrap;
    
    .filter-left {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      width: 150px; // 固定左侧宽度，调整为更小
      
      :deep(.ant-cascader) {
        width: 100%; // 使用父容器的全部宽度
        min-width: 80px; // 设置最小宽度
        max-width: 100px; // 设置最大宽度，调整为更小
        flex-shrink: 0;
        
        .ant-cascader-picker {
          border-radius: 6px;
          border-color: #d9d9d9;
          transition: all 0.2s ease;
          
          &:hover {
            border-color: #40a9ff;
          }
          
          &.ant-cascader-picker-focused {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }
      }
    }
    
    .divider {
      width: 1px;
      height: 16px;
      background: #e8eaec;
      flex-shrink: 0;
    }
    
    .filter-right {
      display: flex;
      align-items: center;
      gap: 6px;
      flex: 1;
      min-width: 0;
      max-width: calc(100% - 120px); // 限制右侧最大宽度，为左侧和分隔线留出空间
      
      :deep(.ant-checkbox-wrapper) {
        font-size: 12px;
        color: #2c3e50;
        white-space: nowrap;
        
        .ant-checkbox {
          .ant-checkbox-inner {
            border-radius: 4px;
          }
        }
      }
      
      .clear-btn {
        padding: 0;
        height: auto;
        font-size: 11px;
        color: #ff4d4f;
        flex-shrink: 0;
        white-space: nowrap;
        
        &:hover {
          color: #ff7875;
        }
      }
    }
  }
  
  .selected-regions-row {
    border-top: 1px solid #e8eaec;
    padding-top: 8px;
    margin-bottom: 0;
    
    .selected-regions {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      
      .region-tag {
        margin: 0;
        border-radius: 3px;
        font-size: 11px;
        padding: 1px 6px;
        background: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: all 0.2s ease;
        line-height: 1.4;
        
        &:hover {
          background: #bae7ff;
          border-color: #69c0ff;
        }
        
        :deep(.ant-tag-close-icon) {
          color: #1890ff;
          font-size: 9px;
          margin-left: 3px;
          
          &:hover {
            color: #ff4d4f;
          }
        }
      }
    }
    
    &.warning-text {
      .warning-message {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #faad14;
        
        .warning-icon {
          margin-right: 4px;
          font-size: 14px;
          color: #faad14;
        }
      }
    }
  }

}

// 响应式设计
@media (max-width: 768px) {
  .region-filter {
    padding: 12px;
    
    .filter-row {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
      
      .divider {
        width: 100%;
        height: 1px;
        margin: 4px 0;
      }
      
             .filter-left,
       .filter-right {
         justify-content: flex-start;
       }
       
       .filter-left {
         :deep(.ant-cascader) {
           width: 100%;
         }
       }
    }
    
    .selected-regions-row {
      .selected-regions {
        .region-tag {
          max-width: 100%;
        }
      }
    }
  }
}
</style> 