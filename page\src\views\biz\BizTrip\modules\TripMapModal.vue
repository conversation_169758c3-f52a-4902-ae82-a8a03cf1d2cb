<template>
  <a-modal
    :visible="visible"
    title="行程地图"
    width="98vw"
    :footer="null"
    @cancel="handleCancel"
    :maskClosable="false"
    :destroyOnClose="true"
    :bodyStyle="{ height: '95vh', padding: '8px' }"
    centered
  >
    <a-spin :spinning="loading" tip="正在加载地图数据...">
      <!-- 左右布局 -->
      <div style="display: flex; height: calc(95vh - 60px); gap: 12px;">
        <!-- 左侧景点列表和控制面板 -->
        <div class="trip-sidebar" style="width: 350px; display: flex; flex-direction: column; padding: 12px;">
          <!-- 控制开关 -->
          <div style="margin-bottom: 12px; padding-bottom: 8px; border-bottom: 1px solid #e8e8e8; display: flex; justify-content: space-between; align-items: center;">
            <span style="color: #666; font-size: 14px;">距离车程显示开关</span>
            <a-switch 
              v-model="showRouteLabels" 
              @change="toggleRouteLabels"
              checked-children="显示"
              un-checked-children="隐藏"
            />
          </div>
          

          
          <!-- 行程统计 -->
          <div class="stats-card">
            <div class="title">行程统计</div>
            <div class="stats-grid">
              <div>总天数：{{ sortedDays.length }}天</div>
              <div>总项目：{{ validScenicCount }}个</div>
              <div>总里程：{{ totalDistance }}km</div>
              <div>总车程：{{ totalDuration }}</div>
            </div>
            <div style="margin-top: 12px; text-align: center;">
              <a-button size="small" @click="toggleAllDays(true)" style="margin-right: 8px;" type="primary">
                <a-icon type="eye" />
                全部显示
              </a-button>
              <a-button size="small" @click="toggleAllDays(false)">
                <a-icon type="eye-invisible" />
                全部隐藏
              </a-button>
            </div>
          </div>
          
          <!-- 景点列表 -->
          <div style="flex: 1; overflow-y: auto;">
            <a-timeline>
              <a-timeline-item v-for="(day, dayIndex) in sortedDays" :key="`day-${dayIndex}`">
                <template slot="dot">
                  <a-icon type="environment" style="font-size: 16px;" :style="{color: dayColors[dayIndex]}" />
                </template>
                <div>
                  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                      <strong style="font-size: 14px;">第{{ day }}天</strong>
                      <span class="visibility-indicator" :class="{ 'visible': dayVisibility[day], 'hidden': !dayVisibility[day] }">
                        <a-icon :type="dayVisibility[day] ? 'eye' : 'eye-invisible'" />
                      </span>
                      <a-switch 
                        v-model="dayVisibility[day]" 
                        @change="toggleDayVisibility(day)"
                        size="small"
                        checked-children="显示"
                        un-checked-children="隐藏"
                        class="day-visibility-switch"
                      />
                    </div>
                    <div style="font-size: 11px; color: #666;">
                      <span v-if="dayStats[day]">
                        {{ dayStats[day].distance }}km / {{ dayStats[day].duration }}
                      </span>
                    </div>
                  </div>
                  <div v-show="dayVisibility[day]" style="margin-left: 0px;">
                    <div v-for="(item, index) in getFilteredItems(scenicByDay[day])" :key="`item-${day}-${index}`" 
                         class="scenic-item">
                      <div style="display: flex; justify-content: space-between; align-items: center; font-size: 13px;">
                        <span style="font-weight: 500;">
                          {{ getTypeIcon(item.bizType) }} {{ item.title }}
                          <!-- 起点标识 -->
                          <span v-if="isStartPoint(day, index)" class="point-tag start-tag">起点</span>
                          <!-- 终点标识 -->
                          <span v-if="isEndPoint(day, index)" class="point-tag end-tag">终点</span>
                        </span>
                        <span v-if="item.longitude && item.latitude" style="color: #52c41a;">
                          <a-icon type="check-circle" />
                        </span>
                        <span v-else style="color: #ff4d4f;">
                          <a-icon type="close-circle" />
                        </span>
                      </div>
                      <!-- 显示到下一个项目的距离（左侧面板始终显示） -->
                      <div v-if="item.nextRouteInfo" style="font-size: 11px; color: #999; margin-top: 4px; padding-left: 8px; border-left: 2px solid #e8e8e8; display: flex; justify-content: space-between; align-items: center;">
                        <span>→ {{ getTransportIcon(item.nextRouteInfo.transportType || 'driving') }} {{ item.nextRouteInfo.distanceText }} / {{ item.nextRouteInfo.durationText }}</span>
                        <a-button size="small" type="link" @click="editRoute(item, 'next')" style="padding: 0; height: auto; font-size: 11px;">
                          <a-icon type="edit" />
                        </a-button>
                      </div>
                      <!-- 显示到次日第一个点的距离（每天最后一个点） -->
                      <div v-if="item.nextDayRouteInfo" style="font-size: 11px; color: #ff7875; margin-top: 4px; padding-left: 8px; border-left: 2px solid #ff7875; background: #fff2f0; padding: 4px 8px; border-radius: 3px; display: flex; justify-content: space-between; align-items: center;">
                        <span>
                          <a-icon type="arrow-right" style="margin-right: 4px;" />
                          次日首站：{{ getTransportIcon(item.nextDayRouteInfo.transportType || 'driving') }} {{ item.nextDayRouteInfo.distanceText }} / {{ item.nextDayRouteInfo.durationText }}
                        </span>
                        <a-button size="small" type="link" @click="editRoute(item, 'nextDay')" style="padding: 0; height: auto; font-size: 11px; color: #ff7875;">
                          <a-icon type="edit" />
                        </a-button>
                      </div>
                    </div>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </div>
        </div>
        
        <!-- 右侧地图 -->
        <div style="flex: 1; position: relative;">
          <div id="trip-map-container" style="width: 100%; height: 100%; border: 1px solid #e8e8e8; border-radius: 4px;">
            <!-- 地图将在这里动态创建 -->
          </div>
        </div>
      </div>
    </a-spin>
    
    <!-- 路线编辑弹窗 -->
    <a-modal
      :visible="routeEditVisible"
      title="编辑路线"
      width="500px"
      @ok="saveRoute"
      @cancel="cancelEditRoute"
      :confirmLoading="routeEditLoading"
    >
      <a-form :form="routeEditForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="起点">
          <a-input :value="routeEditData.originName" disabled />
        </a-form-item>
        <a-form-item label="终点">
          <a-input :value="routeEditData.destinationName" disabled />
        </a-form-item>
        <a-form-item label="交通方式" :validate-status="routeEditErrors.transportType ? 'error' : ''" :help="routeEditErrors.transportType">
          <a-select v-model="routeEditData.transportType" placeholder="请选择交通方式">
            <a-select-option value="driving">🚗 驾车</a-select-option>
            <a-select-option value="railway">🚄 铁路</a-select-option>
            <a-select-option value="flight">✈️ 飞机</a-select-option>
            <a-select-option value="ship">🚢 轮船</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="时长(分钟)" :validate-status="routeEditErrors.duration ? 'error' : ''" :help="routeEditErrors.duration">
          <a-input-number 
            v-model="routeEditData.duration" 
            :min="1" 
            :max="10080" 
            placeholder="请输入时长(分钟)"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="距离(公里)">
          <a-input-number 
            v-model="routeEditData.distance" 
            :min="0" 
            :precision="2"
            placeholder="请输入距离(公里，可选)"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-modal>
</template>

<script>
import { getAction, postAction } from '@/api/manage';

export default {
  name: 'TripMapModal',
  data() {
    return {
      visible: false,
      loading: false,
      tripId: null, // 当前行程ID
      map: null,
      scenicList: [],
      scenicByDay: {},
      routeCache: new Map(),
      dayColors: [
        '#FF6B6B', // 红色 - 第1天
        '#4ECDC4', // 青色 - 第2天  
        '#45B7D1', // 蓝色 - 第3天
        '#96CEB4', // 绿色 - 第4天
        '#FECA57', // 黄色 - 第5天
        '#9B59B6', // 紫色 - 第6天
        '#E67E22', // 橙色 - 第7天
        '#2ECC71', // 翠绿 - 第8天
        '#E74C3C', // 深红 - 第9天
        '#3498DB', // 天蓝 - 第10天
        '#F39C12', // 金黄 - 第11天
        '#1ABC9C', // 青绿 - 第12天
        '#8E44AD', // 深紫 - 第13天
        '#16A085', // 深青 - 第14天
        '#D35400'  // 深橙 - 第15天
      ],
      markers: [],
      polylines: [],
      labels: [],
      showRouteLabels: true,
      dayStats: {},
      totalDistance: 0,
      totalDuration: 0,
      validScenicCount: 0,
      dayVisibility: {}, // 每天的显示状态
      routeConfigs: [], // 路线配置列表
      
      // 路线编辑相关
      routeEditVisible: false,
      routeEditLoading: false,
      routeEditForm: null,
      routeEditData: {
        tripId: null,
        originId: null,
        originType: '',
        originName: '',
        destinationId: null,
        destinationType: '',
        destinationName: '',
        transportType: 'driving',
        duration: null,
        distance: null
      },
      routeEditErrors: {},
      currentEditItem: null,
      currentEditType: null // 'next' 或 'nextDay'
    };
  },
  computed: {
    sortedDays() {
      return Object.keys(this.scenicByDay).sort((a, b) => parseInt(a) - parseInt(b));
    }
  },
  methods: {
    async show(schedule, tripId) {
      this.visible = true;
      this.loading = true;
      this.tripId = tripId; // 保存行程ID
      
      // 等待DOM更新
      await this.$nextTick();
      
      try {
        // 解析schedule数据，提取可展示的项目（景点、酒店、餐厅、常用语）
        const mapItems = schedule.filter(item => 
          item.bizType === 'scenic' || 
          item.bizType === 'hotel' || 
          item.bizType === 'rest' || 
          item.bizType === 'word'
        );
        
        if (mapItems.length === 0) {
          this.$message.warning('行程中没有可展示的地理位置信息');
          this.loading = false;
          return;
        }
        
        // 按天分组
        this.scenicByDay = {};
        mapItems.forEach(item => {
          const day = parseInt(item.resourceId);
          if (!this.scenicByDay[day]) {
            this.scenicByDay[day] = [];
          }
          this.scenicByDay[day].push({
            id: item.bizId,
            title: item.title,
            day: day,
            startTime: item.start,
            bizType: item.bizType
          });
        });
        
        // 对每天的景点按照 startTime 排序
        Object.keys(this.scenicByDay).forEach(day => {
          this.scenicByDay[day].sort((a, b) => {
            const timeA = new Date(a.startTime).getTime();
            const timeB = new Date(b.startTime).getTime();
            return timeA - timeB;
          });
        });
        
        // 初始化每天的显示状态（默认全部显示）
        this.dayVisibility = {};
        Object.keys(this.scenicByDay).forEach(day => {
          this.$set(this.dayVisibility, day, true);
        });
        
        // 批量查询坐标信息
        await this.fetchCoordinates();
        
        // 初始化地图
        await this.initMap();
        
        // 生成地图标记和路线
        await this.generateMapData();
        
        // 获取路线配置
        await this.loadRouteConfigs();
        
        // 计算路线距离和时长
        await this.calculateRoutes();
        
        // 确保地点名字标签正确显示
        this.ensureLocationLabelsVisible();
        
        // 如果标签仍然不显示，尝试使用信息窗口作为备选方案
        setTimeout(() => {
          this.addInfoWindowLabels();
        }, 1000);
        
      } catch (error) {
        console.error('加载地图数据失败:', error);
        this.$message.error('加载地图数据失败');
      } finally {
        this.loading = false;
      }
    },
    
    async initMap() {
      return new Promise((resolve) => {
        // 检查是否已加载腾讯地图API
        if (!window.TMap) {
          const script = document.createElement('script');
          script.src = 'https://map.qq.com/api/gljs?v=1.exp&key=DVSBZ-PL265-5UCIM-IS4ZZ-5DWQJ-IVBUL&libraries=tools,geometry';
          script.onload = () => {
            this.createMap();
            resolve();
          };
          document.head.appendChild(script);
        } else {
          this.createMap();
          resolve();
        }
      });
    },
    
    createMap() {
      const container = document.getElementById('trip-map-container');
      if (!container) {
        console.error('地图容器不存在');
        return;
      }
      
      // 清空容器
      container.innerHTML = '';
      
      try {
        // 创建地图
        this.map = new window.TMap.Map(container, {
          center: new window.TMap.LatLng(39.908491, 116.397470),
          zoom: 10,
          baseMap: {
            type: 'vector'
          },
          showControl: true
        });
        
        console.log('地图创建成功');
      } catch (error) {
        console.error('创建地图失败:', error);
        this.$message.error('地图初始化失败，请刷新重试');
      }
    },
    
    async fetchCoordinates() {
      const allItems = [];
      Object.values(this.scenicByDay).forEach(dayItems => {
        allItems.push(...dayItems);
      });
      
      // 根据类型分组查询
      const promises = allItems.map(item => {
        let apiUrl = '';
        switch (item.bizType) {
          case 'scenic':
            apiUrl = '/biz/bizScenic/queryById';
            break;
          case 'hotel':
            apiUrl = '/biz/bizHotel/queryById';
            break;
          case 'rest':
            apiUrl = '/biz/bizRestaurant/queryById';
            break;
          case 'word':
            apiUrl = '/biz/bizWord/queryById';
            break;
          default:
            return Promise.resolve({ success: false });
        }
        return getAction(apiUrl, { id: item.id });
      });
      
      const results = await Promise.allSettled(promises);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          const data = result.value.result;
          allItems[index].longitude = data.longitude;
          allItems[index].latitude = data.latitude;
          allItems[index].regionAddress = data.regionAddress;
          
          // 根据类型设置名称字段
          switch (allItems[index].bizType) {
            case 'scenic':
              allItems[index].itemName = data.scenicName;
              break;
            case 'hotel':
              allItems[index].itemName = data.hotelName;
              break;
            case 'rest':
              allItems[index].itemName = data.restName;
              break;
            case 'word':
              allItems[index].itemName = data.wordTitle;
              break;
          }
        }
      });
      
      this.scenicList = allItems;
    },
    
    async generateMapData() {
      if (!this.map || !window.TMap) {
        console.error('地图未初始化');
        return;
      }
      
      const validItems = this.scenicList.filter(s => s.longitude && s.latitude);
      
      console.log('有效地理位置数量:', validItems.length);
      console.log('地理位置数据:', validItems);
      
      if (validItems.length === 0) {
        this.$message.warning('没有找到有效的地理位置坐标');
        return;
      }
      
      // 清除旧的标记和标签
      this.markers.forEach(marker => {
        if (marker.setMap) {
          marker.setMap(null);
        }
      });
      this.markers = [];
      this.labels.forEach(label => label.setMap(null));
      this.labels = [];
      
              // 为每个项目创建单独的标记，并添加文字标签
        validItems.forEach((item) => {
          const dayIndex = item.day - 1;
          const color = this.dayColors[dayIndex % this.dayColors.length];
          
          // 根据类型选择图标
          const iconConfig = this.getIconConfig(item.bizType);
          
          // 创建标记
          const marker = new window.TMap.MultiMarker({
            map: this.dayVisibility[item.day] ? this.map : null, // 根据显示状态决定是否显示
            styles: {
              marker: new window.TMap.MarkerStyle({
                width: iconConfig.width,
                height: iconConfig.height,
                anchor: { x: iconConfig.width / 2, y: iconConfig.height },
                src: iconConfig.src
              })
            },
            geometries: [{
              id: `marker-${item.bizType}-${item.id}`,
              styleId: 'marker',
              position: new window.TMap.LatLng(item.latitude, item.longitude),
              properties: {
                title: `D${item.day}-${item.title}`,
                item: item,
                day: item.day // 添加天数信息用于开关控制
              }
            }]
          });
          
          // 添加点击事件
          marker.on('click', (evt) => {
            const item = evt.geometry.properties.item;
            const typeText = this.getTypeText(item.bizType);
            this.$message.info(`${typeText}: ${item.title} - ${item.regionAddress || '暂无地址'}`);
          });
          
          this.markers.push(marker);
        
        // 创建文字标签（项目名字标签始终显示）
        const labelContent = `D${item.day} ${item.title}`;
        const labelId = `label-marker-${item.bizType}-${item.id}`;
        const labelStyleId = `label-style-${item.bizType}-${item.id}`;
        
        try {
          const label = new window.TMap.MultiLabel({
            map: this.dayVisibility[item.day] ? this.map : null, // 根据显示状态决定是否显示
            styles: {
              [labelStyleId]: new window.TMap.LabelStyle({
                color: '#ffffff',
                size: 14,
                offset: { x: 0, y: -50 }, // 固定偏移量，确保标签在标记上方
                alignment: 'center',
                verticalAlignment: 'middle',
                backgroundColor: color,
                borderColor: '#ffffff',
                borderWidth: 2,
                borderRadius: 6,
                padding: { x: 10, y: 6 }, // 添加内边距
                width: 'auto',
                height: 'auto'
              })
            },
            geometries: [{
              id: labelId,
              styleId: labelStyleId,
              position: new window.TMap.LatLng(item.latitude, item.longitude),
              content: labelContent,
              properties: {
                day: item.day // 添加天数信息用于开关控制
              }
            }]
          });
          
          this.labels.push(label); // 标签放入labels数组，用于开关控制
          
          console.log(`地点标签创建成功: ${labelContent} at (${item.latitude}, ${item.longitude}), 显示状态: ${this.dayVisibility[item.day] ? '显示' : '隐藏'}`);
        } catch (error) {
          console.error('创建地点标签失败:', error, item);
        }
        
        // 为起点和终点添加特殊标识
        this.addStartEndMarkers(item, validItems, dayIndex);
      });
      
      console.log('标记创建完成，数量:', this.markers.length);
      
      // 自适应地图视野
      this.fitMapBounds(validItems);
    },
    
    fitMapBounds(items) {
      if (!this.map || items.length === 0) return;
      
      const bounds = new window.TMap.LatLngBounds();
      items.forEach(item => {
        bounds.extend(new window.TMap.LatLng(item.latitude, item.longitude));
      });
      
      this.map.fitBounds(bounds, {
        padding: { top: 50, bottom: 50, left: 50, right: 50 }
      });
    },
    
    async calculateRoutes() {
      if (!this.map || !window.TMap) {
        console.error('地图未初始化，无法计算路线');
        return;
      }
      
      console.log('开始计算路线');
      console.log('所有天数:', this.sortedDays);
      console.log('景点按天分组:', this.scenicByDay);
      
      // 清除旧的路线（但保留景点标签）
      this.polylines.forEach(polyline => polyline.setMap(null));
      this.polylines = [];
      
      // 重置统计数据
      this.dayStats = {};
      this.totalDistance = 0;
      this.totalDuration = 0;
      this.validScenicCount = 0;
      
      let previousDayLastScenic = null; // 记录前一天的最后一个景点
      
      // 按天计算路线
      for (const day of this.sortedDays) {
        const items = this.scenicByDay[day];
        const validItems = items.filter(s => s.longitude && s.latitude);
        
        console.log(`=== 第${day}天 ===`);
        console.log(`有效项目数:`, validItems.length);
        console.log(`项目列表:`, validItems.map(s => s.title));
        
        const dayIndex = parseInt(day) - 1;
        const dayColor = this.dayColors[dayIndex % this.dayColors.length];
        
        console.log(`第${day}天使用颜色:`, dayColor, `(dayIndex: ${dayIndex})`);
        
        console.log(`项目列表（已按startTime排序）:`, validItems.map(s => s.title));
        
        // 初始化当天统计
        let dayDistance = 0;
        let dayDuration = 0;
        
        // 如果有前一天的最后一个项目，连接到当天第一个项目（归属到当天）
        if (previousDayLastScenic && validItems.length > 0) {
          const firstItemToday = validItems[0];
          console.log(`连接相邻天: ${previousDayLastScenic.title} -> ${firstItemToday.title} (归属第${day}天)`);
          
          await new Promise(resolve => setTimeout(resolve, 200));
          
          try {
            // 优先使用数据库配置
            const routeConfig = this.getRouteConfig(
              previousDayLastScenic.id, 
              previousDayLastScenic.bizType,
              firstItemToday.id, 
              firstItemToday.bizType
            );
            
            let routeData;
            if (routeConfig) {
              // 使用数据库配置
              routeData = {
                distance: routeConfig.distance ? routeConfig.distance.toString() : '0',
                duration: routeConfig.duration.toString(),
                distanceText: routeConfig.distance ? `${routeConfig.distance}km` : '未知距离',
                durationText: this.formatDuration(routeConfig.duration),
                transportType: routeConfig.transportType,
                polyline: routeConfig.transportType === 'driving' ? [] : this.createStraightLine(previousDayLastScenic, firstItemToday) // 非驾车方式使用直线
              };
              console.log(`使用数据库配置: ${routeConfig.transportType} ${routeData.distanceText} ${routeData.durationText}`);
            } else {
              // 使用驾车路线规划
              routeData = await this.getDrivingRoute(previousDayLastScenic, firstItemToday);
            }
            
            if (routeData) {
              console.log(`跨天路线数据获取成功，点数: ${routeData.polyline.length}`);
              
              // 保存跨天路线信息到前一天的最后一个项目
              previousDayLastScenic.nextDayRouteInfo = {
                distanceText: routeData.distanceText,
                durationText: routeData.durationText,
                distance: parseFloat(routeData.distance),
                duration: parseInt(routeData.duration),
                transportType: routeData.transportType || 'driving'
              };
              
              // 归属到当天，使用当天颜色
              const styleId = `day-start-route-${day}`;
              try {
                console.log(`创建当天起始路线 ${styleId}，路径点数: ${routeData.polyline.length}`);
                
                // 创建路线（驾车方式使用详细路线，其他方式使用直线）
                if (routeData.polyline && routeData.polyline.length > 0) {
                  const polyline = new window.TMap.MultiPolyline({
                    map: this.dayVisibility[day] ? this.map : null, // 根据显示状态决定是否显示
                    styles: {
                      [styleId]: new window.TMap.PolylineStyle({
                        color: dayColor,
                        width: 4,
                        borderWidth: 1,
                        borderColor: '#FFFFFF',
                        lineCap: 'round',
                        lineJoin: 'round',
                        opacity: 0.9
                      })
                    },
                    geometries: [{
                      id: `day-start-route-${day}`,
                      styleId: styleId,
                      paths: routeData.polyline,
                      properties: {
                        day: day // 添加天数信息用于开关控制
                      }
                    }]
                  });
                
                this.polylines.push(polyline);
                console.log(`当天起始路线创建成功: ${styleId}`);
                
                // 添加清爽的方向箭头
                this.addDirectionArrows(routeData.polyline, dayColor, `day-start-${day}`);
                
                // 添加到当天统计
                dayDistance += parseFloat(routeData.distance);
                dayDuration += parseInt(routeData.duration);
                
                                  // 在路线中点添加距离时长标签
                  if (this.showRouteLabels) {
                    routeData.day = day; // 添加天数信息
                    this.addRouteLabel(routeData, `${routeData.distanceText} ${routeData.durationText}`, dayColor);
                  }
                }
              } catch (error) {
                console.error('创建当天起始路线失败:', error);
              }
            }
          } catch (error) {
            console.error('计算当天起始路线失败:', error);
          }
        }
        
        // 计算当天项目间的路线
        if (validItems.length >= 2) {
          for (let i = 0; i < validItems.length - 1; i++) {
            const origin = validItems[i];
            const destination = validItems[i + 1];
            
            console.log(`计算路线 ${day}-${i}: ${origin.title} -> ${destination.title}`);
            
            // 添加延迟避免并发请求过多
            await new Promise(resolve => setTimeout(resolve, 200));
            
            try {
              // 优先使用数据库配置
              const routeConfig = this.getRouteConfig(
                origin.id, 
                origin.bizType,
                destination.id, 
                destination.bizType
              );
              
              let routeData;
              if (routeConfig) {
                // 使用数据库配置
                routeData = {
                  distance: routeConfig.distance ? routeConfig.distance.toString() : '0',
                  duration: routeConfig.duration.toString(),
                  distanceText: routeConfig.distance ? `${routeConfig.distance}km` : '未知距离',
                  durationText: this.formatDuration(routeConfig.duration),
                  transportType: routeConfig.transportType,
                  polyline: routeConfig.transportType === 'driving' ? [] : this.createStraightLine(origin, destination) // 非驾车方式使用直线
                };
                console.log(`使用数据库配置: ${routeConfig.transportType} ${routeData.distanceText} ${routeData.durationText}`);
              } else {
                // 使用驾车路线规划
                routeData = await this.getDrivingRoute(origin, destination);
              }
              
              if (routeData) {
                console.log(`路线数据获取成功，点数: ${routeData.polyline.length}`);
                
                // 保存路线信息到景点数据中
                origin.nextRouteInfo = {
                  distanceText: routeData.distanceText,
                  durationText: routeData.durationText,
                  distance: parseFloat(routeData.distance),
                  duration: parseInt(routeData.duration),
                  transportType: routeData.transportType || 'driving'
                };
                
                // 添加到当天统计
                dayDistance += parseFloat(routeData.distance);
                dayDuration += parseInt(routeData.duration);
                
                // 创建路线（驾车方式使用详细路线，其他方式使用直线）
                if (routeData.polyline && routeData.polyline.length > 0) {
                  // 创建路线，使用唯一的样式ID
                  const styleId = `route-style-${day}-${i}`;
                  try {
                    console.log(`创建路线 ${styleId}，路径点数: ${routeData.polyline.length}`);
                    console.log(`路径前3个点:`, routeData.polyline.slice(0, 3).map(p => `(${p.lat}, ${p.lng})`));
                    
                    const polyline = new window.TMap.MultiPolyline({
                    map: this.dayVisibility[day] ? this.map : null, // 根据显示状态决定是否显示
                    styles: {
                      [styleId]: new window.TMap.PolylineStyle({
                        color: dayColor,
                        width: 4,
                        borderWidth: 1,
                        borderColor: '#FFFFFF',
                        lineCap: 'round',
                        lineJoin: 'round',
                        opacity: 0.9
                      })
                    },
                    geometries: [{
                      id: `route-${day}-${i}`,
                      styleId: styleId,
                      paths: routeData.polyline,
                      properties: {
                        day: day // 添加天数信息用于开关控制
                      }
                    }]
                  });
                  
                  this.polylines.push(polyline);
                  console.log(`路线创建成功: ${styleId}, 颜色: ${dayColor}`);
                  
                  // 添加清爽的方向箭头
                  this.addDirectionArrows(routeData.polyline, dayColor, `${day}-${i}`);
                  
                  console.log(`路线创建完成，距离: ${routeData.distance}km, 时长: ${routeData.duration}分钟`);
                  
                  // 在当天路线中点添加距离时长标签
                                      if (this.showRouteLabels) {
                      routeData.day = day; // 添加天数信息
                      this.addRouteLabel(routeData, `${routeData.distanceText} ${routeData.durationText}`, dayColor);
                    }
                  } catch (error) {
                    console.error('创建路线失败:', error);
                  }
                } else {
                  console.log(`跳过路线绘制: 交通方式为 ${routeData.transportType || '未知'}`);
                }
              } else {
                console.warn(`无法获取路线数据: ${origin.title} -> ${destination.title}`);
              }
            } catch (error) {
              console.error('计算路线失败:', error);
            }
          }
        } else if (validItems.length === 1) {
          console.log(`第${day}天只有1个项目，跳过当天内部路线计算`);
        } else {
          console.log(`第${day}天没有有效项目，跳过路线计算`);
        }
        
        // 保存当天统计
        this.dayStats[day] = {
          distance: dayDistance.toFixed(1),
          duration: this.formatDuration(dayDuration),
          scenicCount: validItems.length
        };
        
        // 累计总统计
        this.totalDistance += dayDistance;
        this.totalDuration += dayDuration;
        this.validScenicCount += validItems.length;
        
        // 记录当天最后一个项目，用于连接下一天
        if (validItems.length > 0) {
          previousDayLastScenic = validItems[validItems.length - 1];
        }
        
        console.log(`第${day}天路线计算完成`);
      }
      
      // 格式化总统计
      this.totalDistance = this.totalDistance.toFixed(1);
      this.totalDuration = this.formatDuration(this.totalDuration);
      
      console.log('=== 所有路线计算完成 ===');
      console.log('总路线数:', this.polylines.length);
      console.log('总标签数:', this.labels.length);
      console.log('统计数据:', {
        dayStats: this.dayStats,
        totalDistance: this.totalDistance,
        totalDuration: this.totalDuration,
        validScenicCount: this.validScenicCount
      });
      
      // 重新调整地图视野，确保包含所有路线
      if (this.polylines.length > 0) {
        setTimeout(() => {
          this.fitMapToShowAll();
        }, 500);
      }
    },
    
    fitMapToShowAll() {
      if (!this.map || !window.TMap) return;
      
      const bounds = new window.TMap.LatLngBounds();
      let hasValidPoints = false;
      
      // 包含所有有效项目
      this.scenicList.forEach(item => {
        if (item.longitude && item.latitude) {
          const point = new window.TMap.LatLng(item.latitude, item.longitude);
          bounds.extend(point);
          hasValidPoints = true;
          console.log(`添加项目到边界: ${item.title} (${item.latitude}, ${item.longitude})`);
        }
      });
      
      if (!hasValidPoints) {
        console.warn('没有有效的项目坐标，无法调整地图视野');
        return;
      }
      
      console.log('调整地图视野，边界:', {
        southwest: bounds.getSouthWest(),
        northeast: bounds.getNorthEast()
      });
      
      try {
        this.map.fitBounds(bounds, {
          padding: { top: 50, bottom: 50, left: 50, right: 50 }
        });
        console.log('地图视野调整成功');
      } catch (error) {
        console.error('调整地图视野失败:', error);
        // 如果fitBounds失败，尝试设置中心点和缩放级别
        const center = bounds.getCenter();
        if (center) {
          this.map.setCenter(center);
          this.map.setZoom(8);
          console.log('使用备用方案设置地图中心:', center);
        }
      }
    },
    
    async getDrivingRoute(origin, destination) {
      const cacheKey = `${origin.longitude},${origin.latitude}-${destination.longitude},${destination.latitude}`;
      
      // 检查缓存
      if (this.routeCache.has(cacheKey)) {
        return this.routeCache.get(cacheKey);
      }
      
      const key = 'DVSBZ-PL265-5UCIM-IS4ZZ-5DWQJ-IVBUL';
      const from = `${origin.latitude},${origin.longitude}`;
      const to = `${destination.latitude},${destination.longitude}`;
      // 使用SHORT_DISTANCE策略，不考虑路况，距离优先
      const url = `https://apis.map.qq.com/ws/direction/v1/driving/?from=${from}&to=${to}&policy=SHORT_DISTANCE&output=jsonp&key=${key}`;
      
      return new Promise((resolve) => {
        const script = document.createElement('script');
        const callbackName = 'drivingCallback_' + Math.random().toString(36).substr(2, 9);
        
        window[callbackName] = (res) => {
          document.body.removeChild(script);
          delete window[callbackName];
          
          if (res && res.status === 0 && res.result && res.result.routes && res.result.routes.length > 0) {
            const route = res.result.routes[0];
            console.log('驾车规划API返回:', {
              distance: route.distance,
              duration: route.duration,
              polylineLength: route.polyline ? route.polyline.length : 0,
              polylineFirst10: route.polyline ? route.polyline.slice(0, 10) : []
            });
            
            const polyline = this.decodePolyline(route.polyline);
            const result = {
              distance: (route.distance / 1000).toFixed(1),
              duration: Math.ceil(route.duration),
              polyline: polyline,
              transportType: 'driving', // 驾车路线规划
              // 添加原始距离和时长，用于显示
              distanceText: route.distance >= 1000 ? `${(route.distance / 1000).toFixed(1)}km` : `${route.distance}m`,
              durationText: route.duration >= 60 ? `${Math.floor(route.duration / 60)}h${route.duration % 60}m` : `${route.duration}m`
            };
            
            console.log('解码后路线点数:', polyline.length);
            if (polyline.length > 0) {
              console.log('解码后前3个点:', polyline.slice(0, 3).map(p => `(${p.lat}, ${p.lng})`));
              console.log('解码后后3个点:', polyline.slice(-3).map(p => `(${p.lat}, ${p.lng})`));
            }
            
            // 缓存结果
            this.routeCache.set(cacheKey, result);
            resolve(result);
          } else {
            console.error('驾车规划API返回错误:', res);
            resolve(null);
          }
        };
        
        script.onerror = () => {
          document.body.removeChild(script);
          delete window[callbackName];
          resolve(null);
        };
        
        script.src = url + '&callback=' + callbackName;
        document.body.appendChild(script);
      });
    },
    
    decodePolyline(polyline) {
      if (!window.TMap || !polyline || polyline.length < 2) {
        console.error('decodePolyline: 无效输入参数');
        return [];
      }
      
      const points = [];
      
      // 腾讯地图polyline解码算法 - 根据官方文档修正
      // 坐标解压（返回的点串坐标，通过前向差分进行压缩）
      const kr = 1000000;
      
      console.log('开始解码polyline，原始数据前10个:', polyline.slice(0, 10));
      
      // 先进行前向差分解压
      for (let i = 2; i < polyline.length; i++) {
        polyline[i] = Number(polyline[i - 2]) + Number(polyline[i]) / kr;
      }
      
      // 将解压后的坐标放入点串数组中
      for (let i = 0; i < polyline.length; i += 2) {
        if (i + 1 >= polyline.length) break; // 防止数组越界
        
        const lat = polyline[i];
        const lng = polyline[i + 1];
        
        // 验证坐标有效性
        if (Math.abs(lat) <= 90 && Math.abs(lng) <= 180) {
          points.push(new window.TMap.LatLng(lat, lng));
          
          // 只打印前几个点的详细信息
          if (i < 10) {
            console.log(`点${i/2}: (${lat}, ${lng})`);
          }
        } else {
          console.warn(`decodePolyline: 发现无效坐标 lat:${lat}, lng:${lng}`);
        }
      }
      
      console.log(`解码polyline成功，原始长度: ${polyline.length}, 解码后点数: ${points.length}`);
      if (points.length > 0) {
        console.log(`第一个点:`, points[0]);
        console.log(`最后一个点:`, points[points.length - 1]);
      }
      
      return points;
    },
    
    calculateMidPoint(polyline) {
      if (!polyline || polyline.length === 0 || !window.TMap) {
        console.error('calculateMidPoint: 无效的polyline数据');
        return null;
      }
      
      const midIndex = Math.floor(polyline.length / 2);
      const midPoint = polyline[midIndex];
      
      // 验证坐标是否有效（不应该是0,0或者超出地球范围）
      if (!midPoint || 
          typeof midPoint.lat !== 'number' || 
          typeof midPoint.lng !== 'number' ||
          midPoint.lat === 0 && midPoint.lng === 0 ||
          Math.abs(midPoint.lat) > 90 || 
          Math.abs(midPoint.lng) > 180) {
        console.error('calculateMidPoint: 计算出的中点坐标无效:', midPoint);
        return null;
      }
      
      console.log('calculateMidPoint: 计算中点成功:', midPoint);
      return midPoint;
    },
    
    addRouteLabel(routeData, labelText, color) {
      if (!this.map || !window.TMap || !routeData.polyline || routeData.polyline.length === 0) {
        console.error('addRouteLabel: 无效参数');
        return;
      }

      const midPoint = this.calculateMidPoint(routeData.polyline);
      if (!midPoint) {
        console.error('addRouteLabel: 无法计算中点');
        return;
      }

      try {
        // 创建唯一的标签ID和样式ID
        const labelId = `route-label-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const styleId = `style-${labelId}`; // 每个标签使用唯一的样式ID

        // 直接使用传入的颜色作为背景色，与景点名字标签保持一致
        const backgroundColor = color;
        // 使用纯黑色文字，并添加白色描边效果提高对比度
        const textColor = '#000000';

        // 从routeData中获取天数信息（需要在调用时传递）
        const dayFromRoute = routeData.day || null;
        
        // 创建标签，每个标签都有独立的样式
        const label = new window.TMap.MultiLabel({
          map: (this.showRouteLabels && (!dayFromRoute || this.dayVisibility[dayFromRoute])) ? this.map : null, // 根据开关状态和天数显示状态决定是否显示
          styles: {
            [styleId]: new window.TMap.LabelStyle({
              color: textColor,
              size: 14,
              offset: { x: 0, y: -25 },
              alignment: 'center',
              verticalAlignment: 'middle',
              backgroundColor: '#FFFFFF',
              borderColor: backgroundColor,
              borderWidth: 4,
              width: 140,
              height: 36,
              borderRadius: 10,
              fontWeight: 'bold',
              textShadow: '0 0 3px rgba(255,255,255,0.8), 1px 1px 0 rgba(255,255,255,0.8), -1px -1px 0 rgba(255,255,255,0.8)',
              padding: { x: 14, y: 10 }
            })
          },
          geometries: [{
            id: labelId,
            styleId: styleId,
            position: new window.TMap.LatLng(midPoint.lat, midPoint.lng),
            content: labelText,
            properties: {
              day: dayFromRoute // 添加天数信息用于开关控制
            }
          }]
        });

        this.labels.push(label);

        // 添加指示线
        this.addLabelConnector(midPoint, backgroundColor, dayFromRoute);

        console.log(`路线标签创建成功: ${labelText} at (${midPoint.lat}, ${midPoint.lng})`);
      } catch (error) {
        console.error('创建路线标签失败:', error);
      }
    },
    
    // 添加标签连接线，让标签与路线的关系更明确
    addLabelConnector(centerPoint, color, dayFromRoute) {
      try {
        // 创建一个小圆点作为连接指示
        const circleId = `label-connector-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const circle = new window.TMap.MultiCircle({
          map: (this.showRouteLabels && (!dayFromRoute || this.dayVisibility[dayFromRoute])) ? this.map : null, // 根据开关状态和天数显示状态决定是否显示
          styles: {
            'connector': new window.TMap.CircleStyle({
              color: color,
              fillColor: color,
              radius: 3,
              strokeWidth: 1,
              strokeColor: '#FFFFFF'
            })
          },
          geometries: [{
            id: circleId,
            styleId: 'connector',
            center: new window.TMap.LatLng(centerPoint.lat, centerPoint.lng),
            radius: 3,
            properties: {
              day: dayFromRoute // 添加天数信息用于开关控制
            }
          }]
        });
        
        this.labels.push(circle); // 将圆点也加入labels数组，方便统一清理
      } catch (error) {
        console.error('创建标签连接器失败:', error);
      }
    },
    
    // 获取合适的背景色，确保对比度
    getContrastColor(color) {
      // 如果是浅色，使用深色背景
      const lightColors = ['#FECA57', '#96CEB4', '#4ECDC4', '#2ECC71', '#1ABC9C', '#3498DB'];
      if (lightColors.includes(color)) {
        return this.darkenColor(color, 0.4);
      }
      return color;
    },
    
    // 根据背景色获取合适的文字颜色
    getTextColor(backgroundColor) {
      // 计算颜色亮度
      const hex = backgroundColor.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      
      // 亮度大于128使用黑色文字，否则使用白色文字
      return brightness > 128 ? '#000000' : '#FFFFFF';
    },
    
    // 加深颜色
    darkenColor(color, factor) {
      const hex = color.replace('#', '');
      const r = Math.floor(parseInt(hex.substr(0, 2), 16) * (1 - factor));
      const g = Math.floor(parseInt(hex.substr(2, 2), 16) * (1 - factor));
      const b = Math.floor(parseInt(hex.substr(4, 2), 16) * (1 - factor));
      
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    },
    
    handleCancel() {
      this.visible = false;
      
      // 清理地图资源
      if (this.map) {
        this.markers.forEach(marker => {
          if (marker.setMap) {
            marker.setMap(null);
          }
        });
        this.polylines.forEach(polyline => polyline.setMap(null));
        this.labels.forEach(label => label.setMap(null));
        this.markers = [];
        this.polylines = [];
        this.labels = [];
        this.map.destroy();
        this.map = null;
      }
      
      // 清理数据
      this.scenicList = [];
      this.scenicByDay = {};
      this.routeCache.clear();
      this.dayStats = {};
      this.totalDistance = 0;
      this.totalDuration = 0;
      this.validScenicCount = 0;
      
      console.log('地图资源清理完成');
    },
    toggleRouteLabels() {
      // 切换地图上路线距离标签显示状态（不影响景点名字标签和左侧面板信息）
      this.labels.forEach(label => {
        if (label.setMap && label.geometries) {
          label.geometries.forEach(geometry => {
            // 只控制路线标签，不控制地点名字标签
            if (geometry.id.includes('route-label-') || geometry.id.includes('label-connector-')) {
              // 还需要检查天数显示状态
              const dayFromGeometry = geometry.properties && geometry.properties.day;
              const shouldShow = this.showRouteLabels && (!dayFromGeometry || this.dayVisibility[dayFromGeometry]);
              label.setMap(shouldShow ? this.map : null);
            } else if (geometry.id.includes('label-marker-')) {
              // 地点名字标签始终根据天数显示状态控制，不受路线标签开关影响
              const dayFromGeometry = geometry.properties && geometry.properties.day;
              const shouldShow = !dayFromGeometry || this.dayVisibility[dayFromGeometry];
              label.setMap(shouldShow ? this.map : null);
            }
          });
        }
      });
    },
    
    toggleDirectionArrows() {
      // 方向箭头始终显示，此方法保留以防其他地方调用
      console.log('方向箭头始终显示，无需切换');
    },
    
    formatDuration(minutes) {
      if (minutes < 60) {
        return `${minutes}分钟`;
      } else {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
      }
    },

    
    addDirectionArrows(polyline, color, routeId) {
      // 在路线上添加方向箭头标记
      if (!this.map || !window.TMap || !polyline || polyline.length < 2) {
        console.error('addDirectionArrows: 无效参数');
        return;
      }
      
      try {
        // 计算需要添加箭头的位置点（每隔一定距离添加一个箭头）
        const arrowPositions = this.calculateArrowPositions(polyline);
        
        arrowPositions.forEach((arrowData, index) => {
          const arrowId = `arrow-${routeId}-${index}`;
          const styleId = `arrow-style-${routeId}-${index}`;
          
          // 创建清爽的箭头标记（根据路线所属天数的显示状态决定是否显示）
          const dayFromRouteId = this.extractDayFromRouteId(routeId);
          const arrowMarker = new window.TMap.MultiMarker({
            map: (dayFromRouteId && this.dayVisibility[dayFromRouteId]) ? this.map : null,
            styles: {
              [styleId]: new window.TMap.MarkerStyle({
                width: 12,
                height: 12,
                anchor: { x: 6, y: 6 },
                src: `data:image/svg+xml;base64,${this.createArrowIcon(color, arrowData.rotation)}`
              })
            },
            geometries: [{
              id: arrowId,
              styleId: styleId,
              position: new window.TMap.LatLng(arrowData.position.lat, arrowData.position.lng),
              properties: {
                day: dayFromRouteId // 添加天数信息用于开关控制
              }
            }]
          });
          
          this.markers.push(arrowMarker);
        });
        
        console.log(`为路线 ${routeId} 添加了 ${arrowPositions.length} 个方向箭头`);
        
      } catch (error) {
        console.error('添加方向箭头失败:', error);
      }
    },
    
    calculateArrowPositions(polyline) {
      // 计算箭头位置和旋转角度 - 适中版本，增加一些箭头
      const arrowPositions = [];
      const maxArrows = 5; // 每条路线最大箭头数量，增加到5个
      
      if (polyline.length < 2) return arrowPositions;
      
      // 在路线的关键位置添加箭头：20%、35%、50%、65%、80%处
      const positions = [
        Math.floor(polyline.length * 0.2),  // 20%处
        Math.floor(polyline.length * 0.35), // 35%处
        Math.floor(polyline.length * 0.5),  // 中间50%处
        Math.floor(polyline.length * 0.65), // 65%处
        Math.floor(polyline.length * 0.8)   // 80%处
      ];
      
      positions.forEach(i => {
        if (i >= 0 && i < polyline.length - 1) {
          const currentPoint = polyline[i];
          const nextPoint = polyline[Math.min(i + 5, polyline.length - 1)]; // 使用稍远的点计算方向
          
          // 计算箭头旋转角度（基于当前点到下一点的方向）
          const rotation = this.calculateBearing(currentPoint, nextPoint);
          
          arrowPositions.push({
            position: currentPoint,
            rotation: rotation
          });
        }
      });
      
      return arrowPositions;
    },
    
    calculateBearing(point1, point2) {
      // 计算两点间的方位角（用于箭头旋转）
      const lat1 = point1.lat * Math.PI / 180;
      const lat2 = point2.lat * Math.PI / 180;
      const deltaLng = (point2.lng - point1.lng) * Math.PI / 180;
      
      const x = Math.sin(deltaLng) * Math.cos(lat2);
      const y = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLng);
      
      const bearing = Math.atan2(x, y) * 180 / Math.PI;
      return (bearing + 360) % 360; // 确保角度为正值
    },
    
    createArrowIcon(color, rotation) {
      // 创建清爽的箭头SVG图标 - 更小更简洁
      const svg = `
        <svg width="12" height="12" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg">
          <g transform="rotate(${rotation} 6 6)">
            <path d="M6 1 L10 8 L6 6.5 L2 8 Z" fill="${color}" stroke="white" stroke-width="1" opacity="0.9"/>
          </g>
        </svg>
      `;
      return btoa(unescape(encodeURIComponent(svg)));
    },
    
    extractDayFromRouteId(routeId) {
      // 从路线ID中提取天数
      const match = routeId.match(/(\d+)/);
      return match ? match[1] : null;
    },
    
    // 根据类型获取图标配置
    getIconConfig(bizType) {
      // 统一使用SVG标记图标，避免外部链接失效问题
      const configs = {
        scenic: {
          width: 30,
          height: 40,
          src: this.createDefaultMarkerIcon('#FF6B6B') // 红色
        },
        hotel: {
          width: 28,
          height: 38,
          src: this.createDefaultMarkerIcon('#4ECDC4') // 青色
        },
        rest: {
          width: 26,
          height: 36,
          src: this.createDefaultMarkerIcon('#96CEB4') // 绿色
        },
        word: {
          width: 24,
          height: 34,
          src: this.createDefaultMarkerIcon('#FECA57') // 黄色
        }
      };
      return configs[bizType] || configs.scenic;
    },
    
    // 根据类型获取类型文本
    getTypeText(bizType) {
      const typeTexts = {
        scenic: '景点',
        hotel: '酒店',
        rest: '餐厅',
        word: '常用语'
      };
      return typeTexts[bizType] || '未知';
    },
    
    // 根据类型获取类型图标
    getTypeIcon(bizType) {
      const typeIcons = {
        scenic: '🏞️',
        hotel: '🏨',
        rest: '🍽️',
        word: '📝'
      };
      return typeIcons[bizType] || '📍';
    },
    
    // 过滤项目：只过滤掉没有坐标的餐厅和常用语
    getFilteredItems(items) {
      return items.filter(item => {
        // 如果是餐厅或常用语，必须有坐标才显示
        if (item.bizType === 'rest' || item.bizType === 'word') {
          return item.longitude && item.latitude;
        }
        // 其他类型（景点、酒店等）无论是否有坐标都显示
        return true;
      });
    },
    toggleDayVisibility(day) {
      // 获取切换后的显示状态
      const isVisible = this.dayVisibility[day];
      
      console.log(`切换第${day}天显示状态: ${isVisible ? '显示' : '隐藏'}`);
      
      // 切换地图上该天的所有标记的显示状态
      this.markers.forEach(marker => {
        if (marker.geometries) {
          marker.geometries.forEach(geometry => {
            if ((geometry.properties && geometry.properties.day == day) || 
                (geometry.properties && geometry.properties.item && geometry.properties.item.day == day)) {
              marker.setMap(isVisible ? this.map : null);
              console.log(`标记 ${geometry.id} ${isVisible ? '显示' : '隐藏'}`);
            }
          });
        }
      });
      
      // 切换该天的路线显示状态
      this.polylines.forEach(polyline => {
        if (polyline.geometries) {
          polyline.geometries.forEach(geometry => {
            if ((geometry.properties && geometry.properties.day == day) ||
                geometry.id.includes(`-${day}-`) || 
                geometry.id.includes(`route-${day}`) ||
                geometry.id.includes(`day-start-route-${day}`)) {
              polyline.setMap(isVisible ? this.map : null);
              console.log(`路线 ${geometry.id} ${isVisible ? '显示' : '隐藏'}`);
            }
          });
        }
      });
      
      // 切换该天的标签显示状态
      this.labels.forEach(label => {
        if (label.geometries) {
          label.geometries.forEach(geometry => {
            if ((geometry.properties && geometry.properties.day == day) ||
                geometry.id.includes(`-${day}-`) ||
                geometry.id.includes(`label-marker-`) && geometry.properties && geometry.properties.day == day) {
              label.setMap(isVisible ? this.map : null);
              console.log(`标签 ${geometry.id} ${isVisible ? '显示' : '隐藏'}`);
            }
          });
        }
      });
      
      // 强制刷新地图
      if (this.map) {
        this.map.panBy(0, 0);
      }
    },

    toggleAllDays(visible) {
      // 批量切换所有天数的显示状态
      console.log(`批量${visible ? '显示' : '隐藏'}所有天数`);
      
      Object.keys(this.dayVisibility).forEach(day => {
        this.$set(this.dayVisibility, day, visible);
        
        // 切换地图上该天的所有标记的显示状态
        this.markers.forEach(marker => {
          if (marker.geometries) {
            marker.geometries.forEach(geometry => {
              if ((geometry.properties && geometry.properties.day == day) || 
                  (geometry.properties && geometry.properties.item && geometry.properties.item.day == day)) {
                marker.setMap(visible ? this.map : null);
              }
            });
          }
        });
        
        // 切换该天的路线显示状态
        this.polylines.forEach(polyline => {
          if (polyline.geometries) {
            polyline.geometries.forEach(geometry => {
              if ((geometry.properties && geometry.properties.day == day) ||
                  geometry.id.includes(`-${day}-`) || 
                  geometry.id.includes(`route-${day}`) ||
                  geometry.id.includes(`day-start-route-${day}`)) {
                polyline.setMap(visible ? this.map : null);
              }
            });
          }
        });
        
        // 切换该天的标签显示状态
        this.labels.forEach(label => {
          if (label.geometries) {
            label.geometries.forEach(geometry => {
              if ((geometry.properties && geometry.properties.day == day) ||
                  geometry.id.includes(`-${day}-`) ||
                  geometry.id.includes(`label-marker-`) && geometry.properties && geometry.properties.day == day) {
                label.setMap(visible ? this.map : null);
              }
            });
          }
        });
      });
      
      // 强制刷新地图
      if (this.map) {
        this.map.panBy(0, 0);
      }
      
      this.$message.success(`已${visible ? '显示' : '隐藏'}所有天数`);
    },
    
    addStartEndMarkers(item, allValidItems, dayIndex) {
      // 判断是否为整个行程的起点或终点
      const isGlobalStart = this.isGlobalStartPoint(item);
      const isGlobalEnd = this.isGlobalEndPoint(item);
      
      if (isGlobalStart || isGlobalEnd) {
        const markerType = isGlobalStart ? 'start' : 'end';
        const markerColor = isGlobalStart ? '#52c41a' : '#ff4d4f';
        const markerText = isGlobalStart ? '起' : '终';
        
        try {
          // 创建清爽醒目的起终点标记
          const specialMarker = new window.TMap.MultiMarker({
            map: this.dayVisibility[item.day] ? this.map : null, // 根据显示状态决定是否显示
            styles: {
              [`${markerType}-marker`]: new window.TMap.MarkerStyle({
                width: 32,
                height: 32,
                anchor: { x: 16, y: 16 },
                src: `data:image/svg+xml;base64,${this.createSpecialMarkerIcon(markerColor, markerText)}`
              })
            },
            geometries: [{
              id: `${markerType}-marker-${item.id}`,
              styleId: `${markerType}-marker`,
              position: new window.TMap.LatLng(item.latitude + 0.0008, item.longitude + 0.0008), // 稍微偏移避免重叠
              properties: {
                title: isGlobalStart ? '行程起点' : '行程终点',
                item: item,
                day: item.day // 添加天数信息用于开关控制
              }
            }]
          });
          
          this.markers.push(specialMarker);
          
          // 添加闪烁动画效果的外圈
          this.addStartEndGlow(item, markerColor, markerType);
          
          // 添加文字标签
          this.addStartEndLabel(item, markerType, markerColor);
          
        } catch (error) {
          console.error('创建起终点标记失败:', error);
        }
      }
    },
    
    isGlobalStartPoint(item) {
      // 判断是否为整个行程的起点（第一天的第一个有坐标的项目）
      const firstDay = Math.min(...this.sortedDays.map(d => parseInt(d)));
      const firstDayItems = this.scenicByDay[firstDay].filter(s => s.longitude && s.latitude);
      return firstDayItems.length > 0 && firstDayItems[0].id === item.id;
    },
    
    isGlobalEndPoint(item) {
      // 判断是否为整个行程的终点（最后一天的最后一个有坐标的项目）
      const lastDay = Math.max(...this.sortedDays.map(d => parseInt(d)));
      const lastDayItems = this.scenicByDay[lastDay].filter(s => s.longitude && s.latitude);
      return lastDayItems.length > 0 && lastDayItems[lastDayItems.length - 1].id === item.id;
    },
    
    createSpecialMarkerIcon(color, text) {
      // 创建清爽醒目的起点/终点标记图标
      const svg = `
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <!-- 主圆圈 -->
          <circle cx="16" cy="16" r="14" fill="white" stroke="${color}" stroke-width="4"/>
          <!-- 内圆 -->
          <circle cx="16" cy="16" r="10" fill="${color}"/>
          <!-- 文字 -->
          <text x="16" y="21" text-anchor="middle" fill="white" font-size="12" font-weight="bold">${text}</text>
        </svg>
      `;
      return btoa(unescape(encodeURIComponent(svg)));
    },
    
    createDefaultMarkerIcon(color) {
      // 创建默认标记的SVG图标
      const svg = `
        <svg width="30" height="40" viewBox="0 0 30 40" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 0C6.716 0 0 6.716 0 15c0 8.284 15 25 15 25s15-16.716 15-25C30 6.716 23.284 0 15 0z" fill="${color}" stroke="white" stroke-width="2"/>
          <circle cx="15" cy="15" r="6" fill="white"/>
        </svg>
      `;
      return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svg)))}`;
    },
    
    isStartPoint(day, index) {
      // 判断是否为整个行程的起点
      const firstDay = Math.min(...this.sortedDays.map(d => parseInt(d)));
      return parseInt(day) === firstDay && index === 0;
    },
    
    isEndPoint(day, index) {
      // 判断是否为整个行程的终点
      const lastDay = Math.max(...this.sortedDays.map(d => parseInt(d)));
      const filteredItems = this.getFilteredItems(this.scenicByDay[day]);
      return parseInt(day) === lastDay && index === filteredItems.length - 1;
    },
    
    ensureLocationLabelsVisible() {
      // 确保地点名字标签正确显示
      console.log('确保地点名字标签正确显示，总标签数:', this.labels.length);
      
      this.labels.forEach((label, index) => {
        if (label.geometries) {
          label.geometries.forEach(geometry => {
            if (geometry.id.includes('label-marker-')) {
              // 地点名字标签根据天数显示状态控制
              const dayFromGeometry = geometry.properties && geometry.properties.day;
              const shouldShow = !dayFromGeometry || this.dayVisibility[dayFromGeometry];
              
              // 强制设置显示状态
              try {
                label.setMap(shouldShow ? this.map : null);
                console.log(`地点标签 ${geometry.id} ${shouldShow ? '显示' : '隐藏'} (天数: ${dayFromGeometry})`);
                
                // 如果应该显示但没有显示，尝试重新创建
                if (shouldShow && !label.getMap()) {
                  console.warn(`标签 ${geometry.id} 应该显示但未显示，尝试重新设置`);
                  setTimeout(() => {
                    label.setMap(this.map);
                  }, 100);
                }
              } catch (error) {
                console.error(`设置标签 ${geometry.id} 显示状态失败:`, error);
              }
            }
          });
        } else {
          console.warn(`标签 ${index} 没有geometries属性:`, label);
        }
      });
      
      // 强制刷新地图
      if (this.map) {
        setTimeout(() => {
          this.map.panBy(1, 1);
          this.map.panBy(-1, -1);
        }, 200);
      }
    },
    
    addStartEndGlow(item, color, markerType) {
      // 为起终点添加简洁的光晕效果
      try {
        const glowId = `glow-${markerType}-${item.id}`;
        const glowCircle = new window.TMap.MultiCircle({
          map: this.dayVisibility[item.day] ? this.map : null,
          styles: {
            [glowId]: new window.TMap.CircleStyle({
              color: 'transparent',
              fillColor: color,
              radius: 20,
              strokeWidth: 2,
              strokeColor: color,
              fillOpacity: 0.15,
              strokeOpacity: 0.8
            })
          },
          geometries: [{
            id: glowId,
            styleId: glowId,
            center: new window.TMap.LatLng(item.latitude, item.longitude),
            radius: 20,
            properties: {
              day: item.day
            }
          }]
        });
        
        this.markers.push(glowCircle);
        
      } catch (error) {
        console.error('创建起终点光晕效果失败:', error);
      }
    },
    
    addStartEndLabel(item, markerType, color) {
      // 为起终点添加清爽的文字标签
      try {
        const labelText = markerType === 'start' ? '起点' : '终点';
        const labelId = `label-${markerType}-${item.id}`;
        const styleId = `style-${labelId}`;
        
        const label = new window.TMap.MultiLabel({
          map: this.dayVisibility[item.day] ? this.map : null,
          styles: {
            [styleId]: new window.TMap.LabelStyle({
              color: '#ffffff',
              size: 14,
              offset: { x: 0, y: -45 },
              alignment: 'center',
              verticalAlignment: 'middle',
              backgroundColor: color,
              borderColor: '#ffffff',
              borderWidth: 2,
              borderRadius: 6,
              padding: { x: 8, y: 4 },
              width: 'auto',
              height: 'auto'
            })
          },
          geometries: [{
            id: labelId,
            styleId: styleId,
            position: new window.TMap.LatLng(item.latitude, item.longitude),
            content: labelText,
            properties: {
              day: item.day
            }
          }]
        });
        
        this.labels.push(label);
        
        console.log(`起终点标签创建成功: ${labelText}`);
      } catch (error) {
        console.error('创建起终点标签失败:', error);
      }
    },
    
    addInfoWindowLabels() {
      // 使用信息窗口作为地点标签的备选方案
      console.log('尝试使用信息窗口显示地点名字');
      
      const validItems = this.scenicList.filter(s => s.longitude && s.latitude);
      
      validItems.forEach((item, index) => {
        if (!this.dayVisibility[item.day]) return; // 跳过隐藏的天数
        
        const dayIndex = item.day - 1;
        const color = this.dayColors[dayIndex % this.dayColors.length];
        
        try {
          // 创建一个小的信息窗口作为标签
          const infoWindow = new window.TMap.InfoWindow({
            map: this.map,
            position: new window.TMap.LatLng(item.latitude, item.longitude),
            offset: { x: 0, y: -50 },
            content: `<div style="background: ${color}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; white-space: nowrap; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">D${item.day} ${item.title}</div>`,
            enableCustom: true
          });
          
          // 将信息窗口添加到标签数组中，方便管理
          this.labels.push({
            infoWindow: infoWindow,
            setMap: (map) => {
              if (map) {
                infoWindow.open();
              } else {
                infoWindow.close();
              }
            },
            geometries: [{
              id: `info-window-${item.bizType}-${item.id}`,
              properties: { day: item.day }
            }]
          });
          
          console.log(`信息窗口标签创建成功: D${item.day} ${item.title}`);
        } catch (error) {
          console.error('创建信息窗口标签失败:', error);
        }
      });
    },
    
    // 路线编辑相关方法
    editRoute(item, type) {
      this.currentEditItem = item;
      this.currentEditType = type;
      
      // 准备编辑数据
      let routeInfo = null;
      let destinationItem = null;
      
      if (type === 'next') {
        routeInfo = item.nextRouteInfo;
        // 查找下一个有坐标的项目
        const currentDay = item.day;
        const currentDayItems = this.getFilteredItems(this.scenicByDay[currentDay]); // 使用过滤后的项目
        const currentIndex = currentDayItems.findIndex(i => i.id === item.id);
        if (currentIndex < currentDayItems.length - 1) {
          destinationItem = currentDayItems[currentIndex + 1];
        }
      } else if (type === 'nextDay') {
        routeInfo = item.nextDayRouteInfo;
        // 查找次日第一个有坐标的项目
        const nextDay = item.day + 1;
        const nextDayItems = this.scenicByDay[nextDay];
        if (nextDayItems && nextDayItems.length > 0) {
          const filteredNextDayItems = this.getFilteredItems(nextDayItems); // 使用过滤后的项目
          if (filteredNextDayItems.length > 0) {
            destinationItem = filteredNextDayItems[0];
          }
        }
      }
      
      if (!destinationItem) {
        this.$message.error('无法找到目标地点');
        return;
      }
      
      // 检查起点和终点是否都有坐标
      if (!item.longitude || !item.latitude) {
        this.$message.error('起点缺少坐标信息，无法设置路线');
        return;
      }
      
      if (!destinationItem.longitude || !destinationItem.latitude) {
        this.$message.error('终点缺少坐标信息，无法设置路线');
        return;
      }
      
      // 填充编辑表单数据
      this.routeEditData = {
        tripId: this.tripId,
        originId: parseInt(item.id),
        originType: item.bizType,
        originName: item.title,
        destinationId: parseInt(destinationItem.id),
        destinationType: destinationItem.bizType,
        destinationName: destinationItem.title,
        transportType: (routeInfo && routeInfo.transportType) || 'driving',
        duration: routeInfo ? this.parseDuration(routeInfo.durationText) : null,
        distance: routeInfo ? this.parseDistance(routeInfo.distanceText) : null
      };
      
      this.routeEditErrors = {};
      this.routeEditVisible = true;
    },
    
    parseDuration(durationText) {
      if (!durationText) return null;
      // 解析时长文本，如 "2小时30分钟" -> 150分钟
      let minutes = 0;
      const hourMatch = durationText.match(/(\d+)小时/);
      const minuteMatch = durationText.match(/(\d+)分钟/);
      
      if (hourMatch) {
        minutes += parseInt(hourMatch[1]) * 60;
      }
      if (minuteMatch) {
        minutes += parseInt(minuteMatch[1]);
      }
      
      return minutes > 0 ? minutes : null;
    },
    
    parseDistance(distanceText) {
      if (!distanceText) return null;
      // 解析距离文本，如 "123.5km" -> 123.5
      const match = distanceText.match(/([\d.]+)km/);
      return match ? parseFloat(match[1]) : null;
    },
    
    validateRouteForm() {
      this.routeEditErrors = {};
      let isValid = true;
      
      if (!this.routeEditData.transportType) {
        this.routeEditErrors.transportType = '请选择交通方式';
        isValid = false;
      }
      
      if (!this.routeEditData.duration || this.routeEditData.duration <= 0) {
        this.routeEditErrors.duration = '请输入有效的时长';
        isValid = false;
      }
      
      return isValid;
    },
    
    async saveRoute() {
      if (!this.validateRouteForm()) {
        return;
      }
      
      this.routeEditLoading = true;
      try {
        const response = await postAction('/biz/bizTripRoute/saveOrUpdate', this.routeEditData);
        
        if (response.success) {
          this.$message.success('路线配置保存成功');
          
          // 更新界面显示
          this.updateRouteDisplay();
          
          this.routeEditVisible = false;
        } else {
          this.$message.error(response.message || '保存失败');
        }
      } catch (error) {
        console.error('保存路线配置失败:', error);
        this.$message.error('保存失败');
      } finally {
        this.routeEditLoading = false;
      }
    },
    
    updateRouteDisplay() {
      // 更新当前项目的路线显示信息
      const newRouteInfo = {
        transportType: this.routeEditData.transportType,
        distanceText: this.routeEditData.distance ? `${this.routeEditData.distance}km` : '未知距离',
        durationText: this.formatDuration(this.routeEditData.duration),
        distance: this.routeEditData.distance || 0,
        duration: this.routeEditData.duration
      };
      
      if (this.currentEditType === 'next') {
        this.currentEditItem.nextRouteInfo = newRouteInfo;
      } else if (this.currentEditType === 'nextDay') {
        this.currentEditItem.nextDayRouteInfo = newRouteInfo;
      }
      
      // 重新加载路线配置，确保界面数据与数据库同步
      this.loadRouteConfigs();
    },
    
    cancelEditRoute() {
      this.routeEditVisible = false;
      this.routeEditData = {
        tripId: null,
        originId: null,
        originType: '',
        originName: '',
        destinationId: null,
        destinationType: '',
        destinationName: '',
        transportType: 'driving',
        duration: null,
        distance: null
      };
      this.routeEditErrors = {};
    },
    
    getTransportIcon(transportType) {
      const icons = {
        driving: '🚗',
        railway: '🚄',
        flight: '✈️',
        ship: '🚢'
      };
      return icons[transportType] || '🚗';
    },
    
    async loadRouteConfigs() {
      if (!this.tripId) {
        console.warn('没有行程ID，跳过路线配置加载');
        return;
      }
      
      try {
        const response = await getAction('/biz/bizTripRoute/listByTripId', { tripId: this.tripId });
        
        if (response.success && response.result) {
          this.routeConfigs = response.result;
          console.log('加载路线配置成功:', this.routeConfigs);
        } else {
          console.warn('获取路线配置失败:', response.message);
          this.routeConfigs = [];
        }
      } catch (error) {
        console.error('加载路线配置失败:', error);
        this.routeConfigs = [];
      }
    },
    
    getRouteConfig(originId, originType, destinationId, destinationType) {
      if (!this.routeConfigs || this.routeConfigs.length === 0) {
        return null;
      }
      
      return this.routeConfigs.find(config => 
        config.originId === parseInt(originId) &&
        config.originType === originType &&
        config.destinationId === parseInt(destinationId) &&
        config.destinationType === destinationType
      );
    },
    
    // 创建两点间的直线连接
    createStraightLine(origin, destination) {
      if (!origin || !destination || !origin.longitude || !origin.latitude || 
          !destination.longitude || !destination.latitude || !window.TMap) {
        console.error('createStraightLine: 无效的起点或终点坐标');
        return [];
      }
      
      try {
        const startPoint = new window.TMap.LatLng(origin.latitude, origin.longitude);
        const endPoint = new window.TMap.LatLng(destination.latitude, destination.longitude);
        
        // 计算中点坐标，确保标签显示在线路中间
        const midLat = (origin.latitude + destination.latitude) / 2;
        const midLng = (origin.longitude + destination.longitude) / 2;
        const midPoint = new window.TMap.LatLng(midLat, midLng);
        
        // 创建包含中点的直线路径，这样标签就会显示在真正的中间位置
        const line = [startPoint, midPoint, endPoint];
        
        console.log(`创建直线连接: ${origin.title} -> ${destination.title}`);
        console.log(`起点: (${origin.latitude}, ${origin.longitude})`);
        console.log(`中点: (${midLat}, ${midLng})`);
        console.log(`终点: (${destination.latitude}, ${destination.longitude})`);
        
        return line;
      } catch (error) {
        console.error('创建直线连接失败:', error);
        return [];
      }
    }
  },
  beforeDestroy() {
    // 确保组件销毁时清理地图
    if (this.map) {
      this.map.destroy();
      this.map = null;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .ant-modal-body {
  padding: 16px;
}

::v-deep .ant-timeline-item-content {
  margin-left: 20px;
  top: -2px;
}

::v-deep .ant-timeline-item-head {
  top: 12px;
}

::v-deep .ant-timeline-item-head-custom {
  top: 8px;
  left: 8px;
}

::v-deep .ant-timeline-item {
  padding-bottom: 12px;
}

#trip-map-container {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.trip-sidebar {
  background: #fafafa;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.stats-card .title {
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 8px;
}

.stats-card .stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  font-size: 13px;
}

.scenic-item {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 6px 10px;
  margin-bottom: 4px;
  transition: all 0.2s ease;
}

.scenic-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}



.point-tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  margin-left: 6px;
}

.start-tag {
  background: #52c41a;
  color: white;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.end-tag {
  background: #ff4d4f;
  color: white;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 可见性指示器样式 */
.visibility-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: all 0.3s ease;
  
  &.visible {
    background-color: #52c41a;
    color: white;
    box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
  }
  
  &.hidden {
    background-color: #ff4d4f;
    color: white;
    box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
  }
  
  .anticon {
    font-size: 12px;
  }
}

/* 天数显示开关样式 */
::v-deep .day-visibility-switch {
  .ant-switch {
    background-color: #ff4d4f !important;
    border: 1px solid #ff4d4f !important;
    min-width: 50px !important;
    
    &.ant-switch-checked {
      background-color: #52c41a !important;
      border-color: #52c41a !important;
    }
    
    .ant-switch-inner {
      font-size: 10px !important;
      font-weight: 500 !important;
      color: white !important;
      margin-left: 20px !important;
      margin-right: 6px !important;
    }
    
    &:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
      transform: scale(1.05);
    }
    
    &:focus {
      box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}
</style> 