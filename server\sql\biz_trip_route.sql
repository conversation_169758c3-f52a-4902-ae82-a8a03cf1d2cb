-- 行程路线配置表
CREATE TABLE `biz_trip_route` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trip_id` bigint(20) NOT NULL COMMENT '行程ID',
  `origin_id` bigint(20) NOT NULL COMMENT '起点ID',
  `origin_type` varchar(20) NOT NULL COMMENT '起点类型(scenic/hotel/rest/word)',
  `destination_id` bigint(20) NOT NULL COMMENT '终点ID', 
  `destination_type` varchar(20) NOT NULL COMMENT '终点类型(scenic/hotel/rest/word)',
  `transport_type` varchar(20) NOT NULL DEFAULT 'driving' COMMENT '交通方式(driving/railway/flight/ship)',
  `duration` int(11) NOT NULL COMMENT '时长(分钟)',
  `distance` decimal(10,2) DEFAULT NULL COMMENT '距离(公里)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trip_route` (`trip_id`,`origin_id`,`origin_type`,`destination_id`,`destination_type`),
  KEY `idx_trip_id` (`trip_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='行程路线配置表'; 