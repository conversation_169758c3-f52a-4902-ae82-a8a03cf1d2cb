<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="确定">
    
    <a-spin :spinning="confirmLoading">
      <div v-if="materialData">
        <a-row :gutter="16">
          <a-col :span="12">
            <div style="text-align:center">
              <div style="margin-bottom: 10px;">
                <strong>小程序码</strong>
              </div>
              <div v-if="miniProgramQrCode" style="margin-bottom: 10px;">
                <img :src="miniProgramQrCode" style="width: 200px; height: 200px;" />
              </div>
              <div v-else style="height: 200px; display: flex; align-items: center; justify-content: center; border: 1px dashed #d9d9d9;">
                <a-spin v-if="loadingMiniProgram" />
                <span v-else style="color: #999;">暂无小程序码</span>
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div style="text-align:center">
              <div style="margin-bottom: 10px;">
                <strong>H5二维码</strong>
              </div>
              <div v-if="qrCodeUrl && showQrCode" style="margin-bottom: 10px;">
                <qrcode-vue :value="qrCodeUrl" :size="200" :key="qrCodeUrl" />
              </div>
              <div v-else style="height: 200px; display: flex; align-items: center; justify-content: center; border: 1px dashed #d9d9d9;">
                <span style="color: #999;">暂无H5二维码</span>
              </div>
              <div style="margin-top: 10px;">
                <a-input 
                  v-model="h5Url" 
                  :readonly="true"
                  style="margin-bottom: 10px; font-size: 12px;"
                />
                <a-button type="primary" @click="copyH5Url" style="width: 100%;">
                  复制H5链接
                </a-button>
                <a-input ref="copyH5" :value="h5Url" style="position: absolute; left: -9999px;" />
              </div>
            </div>
          </a-col>
        </a-row>
        
        <a-divider />
        
        <div style="margin-top: 20px;">
          <div style="margin-bottom: 10px;">
            <strong>素材信息</strong>
          </div>
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="素材名称">{{ materialData.materialName }}</a-descriptions-item>
            <a-descriptions-item label="素材类型">
              <a-tag :color="materialData.materialType === 'image' ? 'blue' : 'green'">
                {{ materialData.materialType === 'image' ? '图片素材' : '富文本素材' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="标签" :span="2">
              <span v-if="materialData.tags">
                <a-tag v-for="tag in materialData.tags.split(',')" :key="tag" style="margin-right: 8px;">
                  {{ tag.trim() }}
                </a-tag>
              </span>
              <span v-else style="color: #999;">无标签</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction } from '@/api/manage'
import QrcodeVue from 'qrcode.vue'

export default {
  name: 'ShowMaterialLinkModal',
  components: {
    QrcodeVue
  },
  data() {
    return {
      title: '素材二维码',
      visible: false,
      confirmLoading: false,
      loadingMiniProgram: false,
      materialData: null,
      accessKey: '',
      miniProgramQrCode: '',
      h5Url: '',
      h5BaseUrl: 'https://www.woyaotuanjian.com/uni',
      showQrCode: false
    }
  },
  computed: {
    // 确保 h5Url 的响应性
    qrCodeUrl() {
      return this.h5Url || '';
    }
  },
  methods: {
    show(record) {
      this.visible = true;
      this.materialData = record;
      this.confirmLoading = true;
      
      // 生成访问密钥
      this.generateAccessKey(record.id);
    },
    
    generateAccessKey(materialId) {
      getAction('/biz/bizMaterial/generateAccessKey', { id: materialId })
        .then((res) => {
          if (res.success) {
            // 兼容处理：优先使用result，如果为空则使用message
            this.accessKey = res.result || res.message;
            if (this.accessKey) {
              this.generateUrls();
              this.getMiniProgramQrCode();
            } else {
              this.$message.error('访问密钥为空');
              console.error('访问密钥为空:', res);
            }
          } else {
            this.$message.error('生成访问密钥失败: ' + (res.message || '未知错误'));
            console.error('生成访问密钥失败:', res);
          }
        })
        .catch(error => {
          this.$message.error('生成访问密钥时发生错误');
          console.error('生成访问密钥错误:', error);
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    
    generateUrls() {
      // 检查访问密钥
      if (!this.accessKey) {
        console.error('访问密钥为空，无法生成H5链接');
        this.$message.error('访问密钥为空，无法生成H5链接');
        return;
      }
      
      // 生成H5链接
      this.h5Url = `${this.h5BaseUrl}/#/pages/detail/MaterialDetail?id=${this.materialData.id}&key=${this.accessKey}`;
      
      // 延迟显示二维码，确保数据已经设置
      this.$nextTick(() => {
        setTimeout(() => {
          this.showQrCode = true;
        }, 100);
      });
    },
    
    getMiniProgramQrCode() {
      // 检查accessKey是否存在
      if (!this.accessKey) {
        console.error('accessKey为空，无法生成小程序码');
        this.$message.error('访问密钥为空，无法生成小程序码');
        return;
      }
      
      this.loadingMiniProgram = true;
      
      // 构造请求参数
      const params = {
        page: 'pages/detail/MaterialDetail',  // 小程序页面路径
        materialId: this.materialData.id.toString(),
        accessKey: this.accessKey
      };

      getAction('/biz/bizShop/getMaterialMiniProgramQrCode', params)
        .then((res) => {
          if (res.success && res.result) {
            this.miniProgramQrCode = 'data:image/png;base64,' + res.result;
          } else {
            this.$message.warning('获取小程序二维码失败');
            console.error('获取小程序二维码失败:', res);
          }
        })
        .catch(error => {
          this.$message.error('获取小程序二维码时发生错误');
          console.error('获取小程序二维码错误:', error);
        })
        .finally(() => {
          this.loadingMiniProgram = false;
        });
    },
    
    copyH5Url() {
      this.$refs.copyH5.select();
      document.execCommand('copy');
      this.$message.success('H5链接已复制到剪贴板');
    },
    
    handleOk() {
      this.handleCancel();
    },
    
    handleCancel() {
      this.visible = false;
      this.materialData = null;
      this.accessKey = '';
      this.miniProgramQrCode = '';
      this.h5Url = '';
      this.showQrCode = false;
      this.confirmLoading = false;
      this.loadingMiniProgram = false;
    }
  }
}
</script>

<style scoped>
.ant-descriptions-item-label {
  font-weight: 600;
}

.ant-tag {
  margin-bottom: 4px;
}
</style> 