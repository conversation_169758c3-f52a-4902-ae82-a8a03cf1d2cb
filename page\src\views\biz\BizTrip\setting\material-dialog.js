/**
 * 素材库编辑器集成 - 适配TinyMCE编辑器
 */
import Vue from 'vue'
import MaterialSelector from '@/components/MaterialSelector/MaterialSelector.vue'

export function initMaterialSelector(editor) {
  // 注册素材库按钮
  editor.ui.registry.addButton('material', {
    icon: 'image',
    text: '素材库',
    tooltip: '插入图文素材',
    onAction: () => {
      console.log('素材库按钮被点击');
      // 保存光标位置书签，2 表示包含位置，true 表示深度复制
      const bookmark = editor.selection.getBookmark(2, true);
      console.log('已保存光标书签', bookmark);
      
      // 创建素材选择器组件实例
      const MaterialSelectorComponent = Vue.extend(MaterialSelector);
      const materialSelectorInstance = new MaterialSelectorComponent({
        propsData: {
          visible: true
        }
      });

      // 挂载组件
      materialSelectorInstance.$mount();
      document.body.appendChild(materialSelectorInstance.$el);
      console.log('素材选择器已挂载');

      // 安全销毁组件的函数
      const safeDestroy = () => {
        try {
          if (materialSelectorInstance && !materialSelectorInstance._isDestroyed) {
            // 检查元素是否还在DOM中
            if (materialSelectorInstance.$el && materialSelectorInstance.$el.parentNode) {
              materialSelectorInstance.$el.parentNode.removeChild(materialSelectorInstance.$el);
            }
            materialSelectorInstance.$destroy();
            console.log('组件已安全销毁');
          }
        } catch (error) {
          console.warn('销毁组件时出现警告：', error);
        }
      };

      // 监听选择事件
      materialSelectorInstance.$on('select', (material) => {
        console.log('收到选择事件，素材：', material);
        try {
          // 先恢复光标位置
          if (bookmark) {
            editor.selection.moveToBookmark(bookmark);
            console.log('已恢复光标位置');
          }
        } catch (e) {
          console.warn('恢复书签失败', e);
        }
        insertMaterialToEditor(editor, material);
        // 安全销毁组件
        safeDestroy();
      });

      // 监听取消事件
      materialSelectorInstance.$on('cancel', () => {
        console.log('收到取消事件');
        // 安全销毁组件
        safeDestroy();
      });
    }
  });



  // 更新按钮样式，设置图标颜色
  editor.on('init', () => {
    const button = editor.getContainer().querySelector('[aria-label="插入图文素材"]');
    if (button) {
      // 设置图标颜色为蓝色
      const icon = button.querySelector('.tox-icon svg');
      if (icon) {
        icon.style.fill = '#1890ff';
        icon.style.color = '#1890ff';
      }
      
      // 为按钮添加自定义样式类
      button.classList.add('material-selector-btn');
    }
    
    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
      .material-selector-btn .tox-icon svg {
        fill: #1890ff !important;
        color: #1890ff !important;
      }
      .material-selector-btn:hover .tox-icon svg {
        fill: #40a9ff !important;
        color: #40a9ff !important;
      }
    `;
    document.head.appendChild(style);
  });
}

/**
 * 将素材插入到编辑器中
 * @param {Object} editor TinyMCE编辑器实例
 * @param {Object} material 素材对象
 */
function insertMaterialToEditor(editor, material) {
  console.log('开始插入素材到编辑器');
  console.log('编辑器实例：', editor);
  console.log('素材对象：', material);
  
  if (!material) {
    console.error('素材对象为空');
    return;
  }
  
  if (!material.materialType) {
    console.error('素材类型未定义');
    return;
  }
  
  if (!material.content) {
    console.error('素材内容为空');
    return;
  }
  
  let content = '';
  
  console.log('素材类型：', material.materialType, '类型：', typeof material.materialType);
  
  if (material.materialType === '1') {
    // 插入图片素材
    content = `<p><img src="${material.content}" alt="${material.materialName || '图片'}" style="max-width: 100%; height: auto;" /></p>`;
    console.log('准备插入图片素材');
  } else if (material.materialType === '2') {
    // 插入富文本素材
    content = material.content;
    console.log('准备插入富文本素材');
  } else {
    console.error('未知的素材类型：', material.materialType);
    alert('未知的素材类型：' + material.materialType);
    return;
  }
  
  console.log('准备插入的内容：', content);
  
  try {
    // 确保编辑器获得焦点
    editor.focus();
    
    // 方法1：使用insertContent - 这是在光标位置插入的正确方法
    console.log('使用insertContent方法插入');
    editor.insertContent(content);
    console.log('insertContent执行完成');
    
    // 触发change事件
    editor.fire('change');
    editor.fire('input');
    console.log('触发change和input事件');
    
    console.log('素材插入成功');
    
    // 显示成功消息
    if (window.Vue && window.Vue.prototype.$message) {
      window.Vue.prototype.$message.success('素材插入成功！');
    }
    
  } catch (error) {
    console.error('insertContent方法失败：', error);
    
    // 备用方法：使用execCommand
    try {
      console.log('尝试execCommand方法');
      editor.execCommand('mceInsertContent', false, content);
      
      // 触发change事件
      editor.fire('change');
      editor.fire('input');
      
      console.log('execCommand方法成功');
      
      if (window.Vue && window.Vue.prototype.$message) {
        window.Vue.prototype.$message.success('素材插入成功！');
      }
      
    } catch (execError) {
      console.error('execCommand方法失败：', execError);
      
      // 最后的备用方法：手动DOM操作
      try {
        console.log('尝试手动DOM操作');
        
        const selection = editor.selection;
        const range = selection.getRng();
        
        // 创建文档片段
        const fragment = range.createContextualFragment(content);
        
        // 在光标位置插入
        range.insertNode(fragment);
        
        // 将光标移动到插入内容后面
        range.collapse(false);
        selection.setRng(range);
        
        // 触发change事件
        editor.fire('change');
        editor.fire('input');
        
        console.log('手动DOM操作成功');
        
        if (window.Vue && window.Vue.prototype.$message) {
          window.Vue.prototype.$message.success('素材插入成功！');
        }
        
      } catch (domError) {
        console.error('所有插入方法都失败：', domError);
        
        if (window.Vue && window.Vue.prototype.$message) {
          window.Vue.prototype.$message.error('插入素材失败：' + domError.message);
        } else {
          alert('插入素材失败：' + domError.message);
        }
      }
    }
  }
} 