package com.woyaotuanjian.modules.biz.controller.app;

import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.modules.biz.entity.BizMaterial;
import com.woyaotuanjian.modules.biz.service.IBizMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: APP端素材访问控制器
 * @Author: system
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Api(tags="APP端素材访问")
@RestController
@RequestMapping("/app/material")
@Slf4j
public class AppMaterialController {

    @Autowired
    private IBizMaterialService bizMaterialService;

    /**
     * 获取素材详情（需要访问密钥）
     *
     * @param id 素材ID
     * @param key 访问密钥
     * @return
     */
    @AutoLog(value = "APP端-获取素材详情")
    @ApiOperation(value="APP端-获取素材详情", notes="APP端-获取素材详情")
    @GetMapping(value = "/detail")
    public Result<?> getMaterialDetail(
            @RequestParam(name="id", required=true) Long id,
            @RequestParam(name="key", required=true) String key) {
        try {
            // 查询素材
            BizMaterial material = bizMaterialService.getById(id);
            if (material == null) {
                return Result.error("素材不存在");
            }

            // 验证访问密钥
            if (!validateAccessKey(id, key, material)) {
                return Result.error("访问密钥无效");
            }

            // 只返回必要的字段，不返回敏感信息
            BizMaterial result = new BizMaterial();
            result.setId(material.getId());
            result.setMaterialName(material.getMaterialName());
            result.setMaterialType(material.getMaterialType());
            result.setContent(material.getContent());
            result.setThumbnail(material.getThumbnail());
            result.setTags(material.getTags());

            return Result.ok(result);
        } catch (Exception e) {
            log.error("获取素材详情失败", e);
            return Result.error("获取素材详情失败");
        }
    }

    /**
     * 验证访问密钥
     */
    private boolean validateAccessKey(Long id, String key, BizMaterial material) {
        try {
            // 生成正确的6位访问密钥
            String salt = "mat2024";
            String rawKey = id + "_" + material.getCreateTime().getTime() + "_" + salt;
            String md5Hash = org.apache.commons.codec.digest.DigestUtils.md5Hex(rawKey);
            // 取MD5的前6位作为访问密钥
            String correctKey = md5Hash.substring(0, 6);
            
            return correctKey.equals(key);
        } catch (Exception e) {
            log.error("验证访问密钥失败", e);
            return false;
        }
    }
} 