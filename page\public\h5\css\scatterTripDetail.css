
body{
  background-color: #f5f7ff;
}
#app{
  position: relative;
  box-sizing: border-box;
}
.top-img{
  width: 100%;
}
.slide-fade-leave-to{
  transform: translateX(10px);
  opacity: 0;
}
.card{
    width: 94%;
    position: relative;
    background-color: #fff;
    border-radius: 0.6rem;
    margin: 1rem auto;
    padding: 0.8rem 1rem;
    top:-3rem;
    box-sizing: border-box;
}
.card .title{
    font-size: 1rem;
    font-weight: bold;
    display: -webkit-flex; /* Safari */
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content:  flex-start;
}
.trip-title{
  font-size: 1.2rem;
  font-weight: 700;
  color: #333333;
}
.trip-no{
  font-size: 0.8rem;
  font-weight: 500;
  color: #fff;
  position:absolute;
  top:-1.5rem;
}
.price{
  color: #0185ff;
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0.5rem 0;
}

.price::before{
  content: '￥ ';
  font-size: 0.8rem;
}
.price::after{
  content: ' 起';
  font-size: 0.8rem;
  color: #898786;
  font-weight: 400;
}

.title-icon{
  display: inline-block;
  background-color: #0185ff;
  margin-right: 0.5rem;
  border-radius: 0.1rem;
  width: 0.2rem;
  height: 1rem;
  position: relative;
}
.card .content{
    font-size: 0.8rem;
}
.card .content img{
    width: 100%;
    margin:0.5rem 0;
}
.trip-title-card .title{
  color: #333;
}

.trip-desc-card .content{
  font-size: .9rem;
  color: #666;
  padding: 0.5rem 0;
}
.search-tag-container{
  display: -webkit-flex; /* Safari */
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  /* justify-content:space-around; */
}
.search-tag{
  margin-right: 0.4rem;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  padding: 0.2rem 0.6rem;
  border-radius: 0.2rem;
}

.timeline-node-title{
  font-size: 0.9rem;
  font-weight: bold;
}
.timeline-node-title::before{
  content: '⊙';
  background-color: #fff;
  margin-right: 0.5rem;
  color: #0185ff;
  font-size: 1.1rem;
  font-weight: bold;
}
.timeline-node-content{
  font-size: 0.9rem;
  border-left: dotted #999 0.08rem;
  margin-left: 0.5rem;
  padding-left: 1rem;
  color: #666;
  padding-top: 0.3rem;
  padding-bottom: 1rem;
}
.timeline-node:last-child > .timeline-node-content{
  border-left: 0;
} 
.day-title{
  font-size: 0.9rem;
  font-weight: bold;
  color: #0185ff;
  margin: 0.5rem 0;
}
.trip-tip-card .content{
  color: #666;
  font-size: 0.9rem;
}
.second-title{
  font-size: 0.9rem;
  font-weight: bold;
  color: #000;
  margin: 0.5rem 0;
} 
.second-title::after{
  content: '：';
} 
.price-item{
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

  
.footer {
  position: relative;
  width: 100%;
  background-color: #fff;
}
.footer-content {
  width: 100%;
  display: -webkit-flex; /* Safari */
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}
.footer img{
  width: 2rem;
  height: 2rem;
  margin: 0.5rem 0;
  display: inline-block;
}

.footer-content .phone{
  font-size: 1.2rem;
  display: inline-block;
  color: #3b95ff;
  margin: 0.5rem 0.5rem;
  -webkit-touch-callout: all; /* iOS Safari */
  -webkit-user-select: all; /* Chrome/Safari/Opera */
  -moz-user-select: all; /* Firefox */
  -ms-user-select: all; /* Internet Explorer/Edge */
  user-select: all; 
}

.footer-content a{
  display: inline-block;
  border-radius: 0.7rem;
  background-color: #3b95ff;
  color: #fff;
  padding: 0.2rem 0.5rem;
  font-size: 1rem;
  text-decoration: none;
  line-height: 1rem;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
  background-color: transparent;
}

#loading{
  width: 100vw;
  height: 100vh;
  text-align: center;
  background-color: #fff;
  color:#0185FF;
  position: absolute;
  z-index: 9999;
}
#loading > div{
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}
.qrcode-container{
  position: relative;
  display: block;
  padding: 2rem 0;
  width: 100%;
  text-align: center;
  color: #666;
}
.qrcode {
  display: inline-block;
  width: 10rem;
  height: 10rem;
}


.email-info-container{
  width: 90vw;
  max-width: 750px;
  padding: 1rem;
  box-sizing: border-box;
}

.modal-title{
  padding: 10px 16px;
  font-size: 1rem;
  color: #646566;
}
.empty{
  height:1rem;
  width: 100%;
}

.desc-content-title {
  font-size: 0.9rem;
  font-weight: bold;
}

.desc-content {
  font-size: 0.9rem;
  white-space: pre-wrap
}