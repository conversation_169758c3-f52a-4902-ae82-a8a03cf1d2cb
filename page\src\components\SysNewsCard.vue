<template>
  <a-card 
    :bordered="false" 
    :loading="loading"
    class="news-card"
  >
    <template slot="title">
      <div class="card-title">
        <a-icon type="sound" class="title-icon" />
        最新动态
      </div>
    </template>
    <template slot="extra">
      <a-icon 
        type="reload" 
        @click="loadNews" 
        :spin="loading"
        style="cursor: pointer;"
      />
    </template>
    
    <div v-if="newsList.length > 0">
      <div
        v-for="(item, index) in displayNewsList"
        :key="item.id"
        class="news-item"
        @click="showNewsDetail(item)"
      >
        <div class="news-header">
          <div class="news-title">
            <a-icon 
              :type="item.type === 'notice' ? 'notification' : 'code'" 
              :style="{ color: item.type === 'notice' ? '#1890ff' : '#52c41a', marginRight: '8px' }"
            />
            <span class="title-text">{{ item.title }}</span>
            <a-tag 
              v-if="item.isTop === 1" 
              color="red" 
              size="small"
              style="margin-left: 8px;"
            >
              置顶
            </a-tag>
          </div>
          <div class="news-meta">
            <a-tag 
              :color="item.type === 'notice' ? 'blue' : 'green'" 
              size="small"
            >
              {{ item.type === 'notice' ? '通知' : '版本更新' }}
            </a-tag>
            <span class="news-time">{{ formatTime(item.createTime) }}</span>
          </div>
        </div>
        
        <div class="news-content" v-html="getContentPreview(item.content)"></div>
      </div>
      
      <!-- 查看更多按钮 -->
      <div v-if="newsList.length > 3" class="expand-btn">
        <a @click="showAllNews">
          查看全部动态 ({{ newsList.length }})
          <a-icon type="arrow-right" />
        </a>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <a-empty 
        description="暂无动态"
        :image="simpleImage"
      />
    </div>
    
    <!-- 详情弹窗 -->
    <a-modal
      title="动态详情"
      :width="800"
      :visible="detailVisible"
      :footer="null"
      @cancel="detailVisible = false"
    >
      <div v-if="currentNews">
        <div class="detail-header">
          <h3>
            <a-icon 
              :type="currentNews.type === 'notice' ? 'notification' : 'code'" 
              :style="{ color: currentNews.type === 'notice' ? '#1890ff' : '#52c41a', marginRight: '8px' }"
            />
            {{ currentNews.title }}
            <a-tag 
              v-if="currentNews.isTop === 1" 
              color="red" 
              style="margin-left: 8px;"
            >
              置顶
            </a-tag>
          </h3>
          <div class="detail-meta">
            <a-tag 
              :color="currentNews.type === 'notice' ? 'blue' : 'green'"
            >
              {{ currentNews.type === 'notice' ? '通知' : '版本更新' }}
            </a-tag>
            <span style="margin-left: 8px; color: #999;">
              {{ currentNews.createTime }}
            </span>
          </div>
        </div>
        
        <a-divider />
        
        <div class="detail-content" v-html="currentNews.content"></div>
      </div>
    </a-modal>

    <!-- 全部动态弹窗 -->
    <a-modal
      title="全部动态"
      :width="900"
      :visible="allNewsVisible"
      :footer="null"
      @cancel="allNewsVisible = false"
      :bodyStyle="{ maxHeight: '70vh', overflowY: 'auto' }"
    >
      <div class="all-news-container">
        <div
          v-for="(item, index) in newsList"
          :key="item.id"
          class="news-item-modal"
          @click="showNewsDetailFromAll(item)"
        >
          <div class="news-header">
            <div class="news-title">
              <a-icon 
                :type="item.type === 'notice' ? 'notification' : 'code'" 
                :style="{ color: item.type === 'notice' ? '#1890ff' : '#52c41a', marginRight: '8px' }"
              />
              <span class="title-text">{{ item.title }}</span>
              <a-tag 
                v-if="item.isTop === 1" 
                color="red" 
                size="small"
                style="margin-left: 8px;"
              >
                置顶
              </a-tag>
            </div>
            <div class="news-meta">
              <a-tag 
                :color="item.type === 'notice' ? 'blue' : 'green'" 
                size="small"
              >
                {{ item.type === 'notice' ? '通知' : '版本更新' }}
              </a-tag>
              <span class="news-time">{{ formatTime(item.createTime) }}</span>
            </div>
          </div>
          
          <div class="news-content" v-html="getContentPreview(item.content)"></div>
          
          <div class="news-actions">
            <a @click.stop="showNewsDetailFromAll(item)">
              查看详情 <a-icon type="arrow-right" />
            </a>
          </div>
        </div>
      </div>
    </a-modal>
  </a-card>
</template>

<script>
import { getAction } from '@/api/manage';
import { Empty } from 'ant-design-vue';

export default {
  name: 'SysNewsCard',
  data() {
    return {
      loading: false,
      newsList: [],
      detailVisible: false,
      allNewsVisible: false,
      currentNews: null,
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE
    };
  },
  computed: {
    displayNewsList() {
      // 始终只显示前3条
      return this.newsList.slice(0, 3);
    }
  },
  created() {
    this.loadNews();
  },
  methods: {
    loadNews() {
      this.loading = true;
      getAction('/sys/news/getVisibleNews')
        .then(res => {
          if (res.success) {
            this.newsList = res.result || [];
          }
        })
        .catch(err => {
          console.error('加载动态失败:', err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    showAllNews() {
      this.allNewsVisible = true;
    },
    
    showNewsDetailFromAll(item) {
      this.currentNews = item;
      this.allNewsVisible = false;
      this.detailVisible = true;
    },
    
    showNewsDetail(item) {
      this.currentNews = item;
      this.detailVisible = true;
    },
    
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      const now = new Date();
      const diff = now - date;
      
      // 小于1分钟
      if (diff < 60 * 1000) {
        return '刚刚';
      }
      // 小于1小时
      if (diff < 60 * 60 * 1000) {
        return Math.floor(diff / (60 * 1000)) + '分钟前';
      }
      // 小于1天
      if (diff < 24 * 60 * 60 * 1000) {
        return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
      }
      // 小于7天
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';
      }
      
      // 超过7天显示具体日期
      return date.toLocaleDateString();
    },
    
    getContentPreview(content) {
      if (!content) return '';
      
      // 移除HTML标签，获取纯文本
      const textContent = content.replace(/<[^>]*>/g, '');
      
      // 截取前80个字符
      if (textContent.length > 80) {
        return textContent.substring(0, 80) + '...';
      }
      
      return textContent;
    }
  }
};
</script>

<style scoped>
.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
}

.title-icon {
  margin-right: 8px;
  color: #595959;
  font-size: 20px;
}

.news-card {
  margin-top: 24px;
}

@media (max-width: 1199px) {
  .news-card {
    margin-top: 24px;
  }
}

@media (max-width: 991px) {
  .news-card {
    margin-top: 24px;
  }
}

.news-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.news-item:hover {
  background-color: #fafafa;
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
}

.news-item:last-child {
  border-bottom: none;
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.news-title {
  display: flex;
  align-items: center;
  flex: 1;
}

.title-text {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
  line-height: 1.4;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.news-time {
  color: #999;
  font-size: 12px;
}

.news-content {
  color: #666;
  font-size: 13px;
  line-height: 1.5;
  margin-top: 8px;
  text-overflow: ellipsis;
  overflow: hidden;
}

.expand-btn {
  text-align: center;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

.expand-btn a {
  color: #1890ff;
  font-size: 13px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.detail-header h3 {
  margin-bottom: 8px;
  color: #262626;
  display: flex;
  align-items: center;
}

.detail-meta {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.detail-content {
  line-height: 1.8;
  color: #262626;
}

.detail-content >>> img {
  max-width: 100%;
  height: auto;
}

.detail-content >>> p {
  margin-bottom: 12px;
}

.detail-content >>> ul, 
.detail-content >>> ol {
  padding-left: 24px;
  margin-bottom: 12px;
}

.detail-content >>> blockquote {
  border-left: 4px solid #1890ff;
  padding-left: 16px;
  margin: 16px 0;
  background-color: #f6f8fa;
  padding: 12px 16px;
}

.detail-content >>> code {
  background-color: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.detail-content >>> pre {
  background-color: #f6f8fa;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
}

/* 全部动态弹窗样式 */
.all-news-container {
  padding: 8px 0;
}

.news-item-modal {
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.news-item-modal:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  transform: translateY(-1px);
}

.news-item-modal:last-child {
  margin-bottom: 0;
}

.news-item-modal .news-header {
  margin-bottom: 12px;
}

.news-item-modal .news-content {
  margin-bottom: 12px;
  color: #666;
  line-height: 1.6;
}

.news-actions {
  text-align: right;
  padding-top: 8px;
  border-top: 1px solid #f5f5f5;
}

.news-actions a {
  color: #1890ff;
  font-size: 13px;
  text-decoration: none;
}

.news-actions a:hover {
  color: #40a9ff;
}
</style> 