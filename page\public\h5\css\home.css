#app {
  position: relative;
  /*padding: 1rem;*/
}

.van-search__content {
  background-color: white;
}

.container {
  padding: 1rem;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.banner-img {
  width: 100%;
  height: 14rem;
  object-fit: cover;
}

.banner-img img {
  border-radius: 1.2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

#loading {
  width: 100vw;
  height: 100vh;
  text-align: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);
  color: #0185FF;
  position: absolute;
  z-index: 9999;
}

#loading > div {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
  font-weight: 500;
  color: #1890ff;
}

.category-list {
  padding: 1.2rem;
  margin: 1rem 0;
  background: #fff;
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/*.children::-webkit-scrollbar {*/
/*  display: none;*/
/*}*/

.children {
  width: 100%;
  /*float: left;*/
  /*overflow-x: auto;*/
}

.children-item {
  min-width: 30%;
  width: 6.9rem;
  float: left;
  margin-right: 10px;
  margin-bottom: 5px;
}

.children-item:nth-child(n+3) {
  margin-right: 0;
}

.align-center {
  text-align: center;
}

.category-title {
  height: 1.4rem;
  line-height: 1.4rem;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 1rem;
  position: relative;
  color: #323233;
}

.category-title::before {
  content: " ";
  position: absolute;
  left: -1rem;
  top: 0.1rem;
  height: 1.2rem;
  width: 0.25rem;
  border-radius: 0.25rem;
  background: linear-gradient(to bottom, #1890ff, #36a6ff);
}

.category-title-2 {
  margin: 0.8rem 0;
  font-size: 0.9rem;
  color: #323233;
  font-weight: 500;
}

.img-wrap {
  position: relative;
  width: 100%;
  height: 0;
  padding-top: 100%;
  transition: transform 0.3s;
}

.img-wrap:active {
  transform: scale(0.98);
}

.img-wrap img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 0.8rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  object-fit: cover;
}

.single-ad {
  margin: 1rem 0;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 底部导航栏样式 */
.van-tabbar {
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
}

.van-tabbar-item--active {
  color: #1890ff !important;
}