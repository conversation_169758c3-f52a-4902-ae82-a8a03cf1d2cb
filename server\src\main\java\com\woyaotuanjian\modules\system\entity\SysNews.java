package com.woyaotuanjian.modules.system.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统动态表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SysNews implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 标题
     */
    @Excel(name="标题",width=30)
    private String title;
    
    /**
     * 内容
     */
    private String content;
    
    /**
     * 类型：notice-通知，version-版本更新
     */
    @Excel(name="类型",width=15)
    private String type;
    
    /**
     * 可见角色列表，逗号分隔
     */
    @Excel(name="可见角色",width=30)
    private String visibleRoles;
    
    /**
     * 是否置顶：0-否，1-是
     */
    @Excel(name="是否置顶",width=15)
    private Integer isTop;
    
    /**
     * 状态：0-禁用，1-启用
     */
    @Excel(name="状态",width=15)
    private Integer status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

} 