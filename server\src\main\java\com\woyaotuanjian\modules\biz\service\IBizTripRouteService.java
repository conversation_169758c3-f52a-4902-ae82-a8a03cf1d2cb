package com.woyaotuanjian.modules.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.woyaotuanjian.modules.biz.entity.BizTripRoute;

import java.util.List;

/**
 * @Description: 行程路线配置
 */
public interface IBizTripRouteService extends IService<BizTripRoute> {

    /**
     * 根据行程ID获取所有路线配置
     */
    List<BizTripRoute> getByTripId(Long tripId);
    
    /**
     * 根据起止点查询路线配置
     */
    BizTripRoute getByRoute(Long tripId, Long originId, String originType, 
                           Long destinationId, String destinationType);
    
    /**
     * 保存或更新路线配置
     */
    boolean saveOrUpdateRoute(BizTripRoute route);
    
    /**
     * 删除行程的所有路线配置
     */
    boolean deleteByTripId(Long tripId);
} 