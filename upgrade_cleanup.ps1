# Node.js升级后的清理和重新安装脚本

Write-Host "=== Node.js升级后清理和重新安装依赖 ===" -ForegroundColor Green

# 验证Node.js版本
Write-Host "`n检查Node.js版本..." -ForegroundColor Yellow
$nodeVersion = node --version
Write-Host "当前Node.js版本: $nodeVersion" -ForegroundColor Cyan

$npmVersion = npm --version
Write-Host "当前npm版本: $npmVersion" -ForegroundColor Cyan

# 进入page目录
Write-Host "`n进入page目录..." -ForegroundColor Yellow
Set-Location -Path "page"

# 清理旧的依赖
Write-Host "`n清理旧的依赖文件..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Write-Host "删除node_modules目录..." -ForegroundColor Red
    Remove-Item -Recurse -Force "node_modules"
}

if (Test-Path "package-lock.json") {
    Write-Host "删除package-lock.json..." -ForegroundColor Red
    Remove-Item -Force "package-lock.json"
}

if (Test-Path "yarn.lock") {
    Write-Host "保留yarn.lock文件..." -ForegroundColor Blue
}

# 清理npm缓存
Write-Host "`n清理npm缓存..." -ForegroundColor Yellow
npm cache clean --force

# 重新安装依赖
Write-Host "`n重新安装依赖..." -ForegroundColor Yellow
npm install

Write-Host "`n=== 清理和重新安装完成 ===" -ForegroundColor Green
