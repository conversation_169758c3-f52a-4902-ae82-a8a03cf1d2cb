package com.woyaotuanjian.modules.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 图文素材库
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Data
@TableName("biz_material")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="biz_material对象", description="图文素材库")
public class BizMaterial implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**素材名称*/
    @Excel(name = "素材名称", width = 15)
    @ApiModelProperty(value = "素材名称")
    private String materialName;
    
    /**素材类型：image-图片素材，richtext-富文本素材*/
    @Excel(name = "素材类型", width = 15, dicCode = "material_type")
    @ApiModelProperty(value = "素材类型：image-图片素材，richtext-富文本素材")
    private String materialType;
    
    /**素材内容：图片素材存储图片URL，富文本素材存储HTML内容*/
    @ApiModelProperty(value = "素材内容")
    private String content;
    
    /**缩略图URL：图片素材直接使用content，富文本素材提取第一张图片*/
    @ApiModelProperty(value = "缩略图URL")
    private String thumbnail;
    
    /**标签分类，多个标签用逗号分隔*/
    @Excel(name = "标签分类", width = 15)
    @ApiModelProperty(value = "标签分类，多个标签用逗号分隔")
    private String tags;
    
    /**状态：1-启用，0-禁用*/
    @Excel(name = "状态", width = 15, dicCode = "yn")
    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    private Integer status;
    
    /**公司ID，用于权限控制*/
    @ApiModelProperty(value = "公司ID，用于权限控制")
    private Long comId;
    
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    
    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    
    /**创建用户ID*/
    @ApiModelProperty(value = "创建用户ID")
    private Long sysUserId;
    
    /**创建用户名*/
    @Excel(name = "创建用户名", width = 15)
    @ApiModelProperty(value = "创建用户名")
    private String sysUserName;
} 