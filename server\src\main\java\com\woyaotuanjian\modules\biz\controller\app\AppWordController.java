package com.woyaotuanjian.modules.biz.controller.app;

import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.modules.biz.entity.BizWord;
import com.woyaotuanjian.modules.biz.service.IBizWordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/app/word")
public class AppWordController {

    @Autowired
    private IBizWordService bizWordService;

    /**
     * 获取常用语详情
     * @param id 常用语ID
     * @return 常用语详情
     */
    @GetMapping("/detail")
    public Result<BizWord> getWordDetail(@RequestParam("id") String id) {
        try {
            BizWord word = bizWordService.getById(id);
            
            Result<BizWord> result = new Result<>();
            result.setResult(word);
            result.success("获取成功");
            return result;
        } catch (Exception e) {
            log.error("Get word detail failed", e);
            Result<BizWord> error = new Result<>();
            error.error500("获取常用语详情失败");
            return error;
        }
    }
} 