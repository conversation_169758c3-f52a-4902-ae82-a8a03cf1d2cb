/** region Flex 布局样式*/
.display-flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}

/*沾满剩余宽度*/
.display-flex-fill {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
}

/*垂直+水平 居中*/
.display-flex-xc-yc {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: center !important; /* 水平居中 */
  align-items: center !important; /* 垂直居中 */
}

/*垂直居中 水平居右*/
.display-flex-xr-yc {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: flex-end !important; /* 水平居右 */
  align-items: center !important; /* 垂直居中 */
}

/*垂直居中*/
.display-flex-yc {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center; /* 垂直居中 */
}

/*垂直居上*/
.display-flex-ys {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: flex-start; /* 垂直居上 */
}

/*垂直居下*/
.display-flex-ye {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: flex-end; /* 垂直居下 */
}

/*水平居中垂直居下*/
.display-flex-xc-ye {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: flex-end; /* 垂直居下 */
}

/*水平居中*/
.display-flex-xc {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: center; /* 水平居中 */
}

.display-flex-grow-0 {
  flex-grow: 1;
  width: 0;
}

.display-flex-grow-1 {
  flex-grow: 1;
}

.display-flex-grow-2 {
  flex-grow: 2;
  width: 0;
}

.display-flex-grow-3 {
  flex-grow: 3;
  width: 0;
}

/** endregion Flex 布局样式*/



/* 强制换行 */
.ty-break {
  word-break: break-all;
}

/* 最多显示一行的文字，后面的内容会省略 */
.ty-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis
}

/* 最多显示两行的文字，后面的内容会省略 */
.ty-multi-ellipsis--l2 {
  -webkit-line-clamp: 2
}

.ty-multi-ellipsis--l2,
.ty-multi-ellipsis--l3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical
}

/* 最多显示三行的文字，后面的内容会省略 */
.ty-multi-ellipsis--l3 {
  -webkit-line-clamp: 3
}
