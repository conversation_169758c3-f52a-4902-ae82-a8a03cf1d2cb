<template>
  <a-card :bordered="false" class="card-area">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <!-- 搜索区域 -->
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="标题" :labelCol="{span: 5}" :wrapperCol="{span: 18, offset: 1}">
              <a-input placeholder="请输入标题查询" v-model="queryParam.title"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="类型" :labelCol="{span: 5}" :wrapperCol="{span: 18, offset: 1}">
              <a-select placeholder="请选择类型" v-model="queryParam.type" allowClear>
                <a-select-option value="notice">通知</a-select-option>
                <a-select-option value="version">版本更新</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" @click="searchQuery">查询</a-button>
            <a-button style="margin-left: 8px" @click="searchReset" type="primary">刷新</a-button>
            <a-button style="margin-left: 8px" @click="handleAdd" type="primary" icon="plus">新增</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择&nbsp;
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项&nbsp;&nbsp;
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange"
      >
        <span slot="type" slot-scope="text">
          <a-tag :color="text === 'notice' ? 'blue' : 'green'">
            {{ text === 'notice' ? '通知' : '版本更新' }}
          </a-tag>
        </span>
        
        <span slot="isTop" slot-scope="text">
          <a-tag :color="text === 1 ? 'red' : 'default'">
            {{ text === 1 ? '置顶' : '普通' }}
          </a-tag>
        </span>
        
        <span slot="status" slot-scope="text">
          <a-tag :color="text === 1 ? 'green' : 'red'">
            {{ text === 1 ? '启用' : '禁用' }}
          </a-tag>
        </span>

        <span slot="action" slot-scope="text, record">
          <a-dropdown>
            <a class="ant-dropdown-link">
              操作
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">查看</a>
              </a-menu-item>
              <a-menu-item>
                <a @click="handleEdit(record)">编辑</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <sys-news-modal ref="modalForm" @ok="modalFormOk"></sys-news-modal>
    <!-- 详情弹窗 -->
    <sys-news-detail-modal ref="detailModal"></sys-news-detail-modal>
  </a-card>
</template>

<script>
import SysNewsModal from './modules/SysNewsModal';
import SysNewsDetailModal from './modules/SysNewsDetailModal';
import { JeecgListMixin } from '@/mixins/JeecgListMixin';

export default {
  name: 'SysNewsList',
  mixins: [JeecgListMixin],
  components: {
    SysNewsModal,
    SysNewsDetailModal
  },
  data() {
    return {
      description: '系统动态管理页面',
      // 查询条件
      queryParam: { 
        title: '',
        type: ''
      },
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1;
          }
        },
        {
          title: '标题',
          align: 'center',
          dataIndex: 'title',
          width: 200
        },
        {
          title: '类型',
          align: 'center',
          dataIndex: 'type',
          width: 100,
          scopedSlots: { customRender: 'type' }
        },
        {
          title: '置顶',
          align: 'center',
          dataIndex: 'isTop',
          width: 80,
          scopedSlots: { customRender: 'isTop' }
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status',
          width: 80,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '创建人',
          align: 'center',
          dataIndex: 'createBy',
          width: 100
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center',
          width: 150,
          sorter: true
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/sys/news/list',
        delete: '/sys/news/delete',
        deleteBatch: '/sys/news/deleteBatch',
        exportXlsUrl: '/sys/news/exportXls',
        importExcelUrl: 'sys/news/importExcel'
      }
    };
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  methods: {
    handleDetail: function(record) {
      this.$refs.detailModal.show(record);
    }
  }
};
</script>
<style scoped>
@import '~@assets/less/common.less';
</style> 