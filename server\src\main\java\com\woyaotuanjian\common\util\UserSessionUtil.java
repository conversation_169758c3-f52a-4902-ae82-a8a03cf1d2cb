package com.woyaotuanjian.common.util;

import com.woyaotuanjian.common.constant.CommonConstant;
import com.woyaotuanjian.common.system.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 用户会话管理工具类
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserSessionUtil {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 踢出指定用户的所有登录会话
     * @param username 用户名
     * @return 踢出的会话数量
     */
    public int kickOutUser(String username) {
        try {
            // 获取所有token相关的key
            Set<String> tokenKeys = redisUtil.keys(CommonConstant.PREFIX_USER_TOKEN + "*");
            int kickedCount = 0;
            
            if (tokenKeys != null && !tokenKeys.isEmpty()) {
                for (String tokenKey : tokenKeys) {
                    // 从key中提取token
                    String token = tokenKey.replace(CommonConstant.PREFIX_USER_TOKEN, "");
                    // 从token中获取用户名
                    String tokenUsername = JwtUtil.getUsername(token);
                    
                    if (username.equals(tokenUsername)) {
                        // 删除该用户的token缓存
                        redisUtil.del(tokenKey);
                        kickedCount++;
                        log.info("踢出用户: {} 的登录会话, token: {}", username, token);
                    }
                }
                
                // 删除用户权限缓存
                if (kickedCount > 0) {
                    redisUtil.del(CommonConstant.LOGIN_USER_CACHERULES_ROLE + username);
                    redisUtil.del(CommonConstant.LOGIN_USER_CACHERULES_PERMISSION + username);
                }
            }
            
            log.info("用户: {} 共踢出 {} 个登录会话", username, kickedCount);
            return kickedCount;
        } catch (Exception e) {
            log.error("踢出用户登录会话失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 踢出指定用户的单个会话
     * @param token 要踢出的token
     * @return 是否成功
     */
    public boolean kickOutSession(String token) {
        try {
            String tokenKey = CommonConstant.PREFIX_USER_TOKEN + token;
            if (redisUtil.hasKey(tokenKey)) {
                String username = JwtUtil.getUsername(token);
                redisUtil.del(tokenKey);
                log.info("踢出用户: {} 的单个登录会话, token: {}", username, token);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("踢出单个登录会话失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取用户当前的活跃会话数量
     * @param username 用户名
     * @return 活跃会话数量
     */
    public int getActiveSessionCount(String username) {
        try {
            Set<String> tokenKeys = redisUtil.keys(CommonConstant.PREFIX_USER_TOKEN + "*");
            int count = 0;
            
            if (tokenKeys != null && !tokenKeys.isEmpty()) {
                for (String tokenKey : tokenKeys) {
                    String token = tokenKey.replace(CommonConstant.PREFIX_USER_TOKEN, "");
                    String tokenUsername = JwtUtil.getUsername(token);
                    
                    if (username.equals(tokenUsername)) {
                        count++;
                    }
                }
            }
            
            return count;
        } catch (Exception e) {
            log.error("获取用户活跃会话数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }
} 