<template>
  <a-modal
    title="选择素材"
    :width="1000"
    :visible="visible"
    :footer="null"
    @cancel="handleCancel">
    
    <div class="material-selector">
      <!-- 搜索区域 -->
      <div class="search-area">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-input 
              v-model="searchParams.materialName" 
              placeholder="搜索素材名称"
              @keyup.enter="handleSearch">
              <a-icon slot="prefix" type="search" />
            </a-input>
          </a-col>
          <a-col :span="8">
            <a-select 
              v-model="searchParams.materialType" 
              placeholder="请选择素材类型" 
              allowClear
              @change="handleSearch"
              style="width: 100%">
              <a-select-option value="1">图片素材</a-select-option>
              <a-select-option value="2">富文本素材</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-input 
              v-model="searchParams.tags" 
              placeholder="标签"
              @keyup.enter="handleSearch">
            </a-input>
          </a-col>
          <a-col :span="4">
            <a-button type="primary" @click="handleSearch" icon="search">搜索</a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 素材列表 -->
      <div class="material-list">
        <a-spin :spinning="loading">
          <div class="material-grid">
            <div 
              v-for="item in materialList" 
              :key="item.id"
              class="material-item"
              @click="selectMaterial(item)">
              
              <!-- 图片素材 -->
              <div v-if="item.materialType === '1'" class="image-material">
                <div class="image-container">
                  <img :src="item.thumbnail" :alt="item.materialName" />
                  <div class="overlay">
                    <a-icon type="eye" class="preview-icon" />
                  </div>
                </div>
                <div class="material-info">
                  <div class="material-name" :title="item.materialName">{{ item.materialName }}</div>
                  <div class="material-type">
                    <a-tag color="blue" size="small">图片素材</a-tag>
                  </div>
                  <div class="material-tags">
                    <a-tag v-for="tag in getTagList(item.tags)" :key="tag" size="small">{{ tag }}</a-tag>
                  </div>
                </div>
              </div>

              <!-- 富文本素材 -->
              <div v-else class="richtext-material">
                <div class="richtext-container">
                  <div class="richtext-preview" v-html="item.content"></div>
                  <div class="overlay">
                    <a-icon type="eye" class="preview-icon" />
                  </div>
                </div>
                <div class="material-info">
                  <div class="material-name" :title="item.materialName">{{ item.materialName }}</div>
                  <div class="material-type">
                    <a-tag color="green" size="small">富文本素材</a-tag>
                  </div>
                  <div class="material-tags">
                    <a-tag v-for="tag in getTagList(item.tags)" :key="tag" size="small">{{ tag }}</a-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && materialList.length === 0" class="empty-state">
            <a-empty description="暂无素材" />
          </div>
        </a-spin>
      </div>

      <!-- 分页 -->
      <div class="pagination-area" v-if="materialList.length > 0">
        <a-pagination
          v-model="pagination.current"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          :show-size-changer="false"
          :show-quick-jumper="true"
          @change="handlePageChange"
          size="small" />
      </div>
    </div>

    <!-- 预览模态框 -->
    <a-modal
      title="素材预览"
      :width="800"
      :visible="previewVisible"
      @cancel="previewVisible = false">
      
      <template slot="footer">
        <a-button @click="previewVisible = false">取消</a-button>
        <a-button type="primary" @click="insertMaterial(previewMaterial)">插入</a-button>
      </template>
      
      <div v-if="previewMaterial">
        <div class="preview-header">
          <h3>{{ previewMaterial.materialName }}</h3>
          <div class="preview-tags">
            <a-tag v-for="tag in getTagList(previewMaterial.tags)" :key="tag">{{ tag }}</a-tag>
          </div>
        </div>
        
        <div class="preview-content">
          <!-- 图片预览 -->
          <div v-if="previewMaterial.materialType === '1'" class="image-preview">
            <img :src="previewMaterial.content" :alt="previewMaterial.materialName" style="max-width: 100%; height: auto;" />
          </div>
          
          <!-- 富文本预览 -->
          <div v-else class="richtext-preview-full" v-html="previewMaterial.content"></div>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script>
  import { getAction } from '@/api/manage'

  export default {
    name: 'MaterialSelector',
    props: {
      visible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: false,
        materialList: [],
        searchParams: {
          materialName: '',
          materialType: undefined, // 使用undefined确保placeholder显示
          tags: ''
        },
        pagination: {
          current: 1,
          pageSize: 12,
          total: 0
        },
        previewVisible: false,
        previewMaterial: null
      }
    },
    watch: {
      visible(val) {
        if (val) {
          this.resetAndLoadMaterials();
        }
      }
    },
    mounted() {
      // 组件挂载时就加载素材
      this.loadMaterials();
    },
    methods: {
      loadMaterials() {
        this.loading = true;
        const params = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          status: 1, // 只加载启用状态的素材
          ...this.searchParams
        };

        console.log('加载素材参数：', params);

        getAction('/biz/bizMaterial/list', params).then(res => {
          console.log('素材加载结果：', res);
          if (res.success) {
            this.materialList = res.result.records || [];
            this.pagination.total = res.result.total || 0;
            console.log('加载到的素材数量：', this.materialList.length);
            console.log('素材列表：', this.materialList);
          } else {
            this.$message.error('加载素材失败：' + res.message);
          }
        }).catch(err => {
          this.$message.error('加载素材失败');
          console.error('加载素材错误：', err);
        }).finally(() => {
          this.loading = false;
        });
      },
      
      resetAndLoadMaterials() {
        // 重置搜索条件和分页
        this.searchParams = {
          materialName: '',
          materialType: undefined, // 使用undefined确保placeholder显示
          tags: ''
        };
        this.pagination.current = 1;
        this.loadMaterials();
      },
      
      handleSearch() {
        this.pagination.current = 1;
        this.loadMaterials();
      },
      
      handlePageChange(page) {
        this.pagination.current = page;
        this.loadMaterials();
      },
      
      selectMaterial(material) {
        this.previewMaterial = material;
        this.previewVisible = true;
      },
      
      insertMaterial(material) {
        this.$emit('select', material);
        this.previewVisible = false;
        this.handleCancel();
      },
      
      handleCancel() {
        this.$emit('cancel');
      },
      
      getTagList(tags) {
        if (!tags) return [];
        return tags.split(',').filter(tag => tag.trim()).map(tag => tag.trim());
      }
    }
  }
</script>

<style scoped>
  .material-selector {
    padding: 0;
  }

  .search-area {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;
  }

  .material-list {
    padding: 16px;
    min-height: 400px;
  }

  .material-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
  }

  .material-item {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;
    background: white;
  }

  .material-item:hover {
    border-color: #40a9ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .image-material .image-container,
  .richtext-material .richtext-container {
    position: relative;
    height: 120px;
    overflow: hidden;
  }

  .image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .richtext-container {
    padding: 8px;
    background-color: #fafafa;
  }

  .richtext-preview {
    font-size: 12px;
    line-height: 1.4;
    height: 104px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .material-item:hover .overlay {
    opacity: 1;
  }

  .preview-icon {
    color: white;
    font-size: 24px;
  }

  .material-info {
    padding: 12px;
  }

  .material-name {
    font-weight: 500;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .material-type {
    margin-bottom: 6px;
  }

  .material-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .pagination-area {
    padding: 16px;
    text-align: center;
    border-top: 1px solid #f0f0f0;
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }

  .preview-header {
    margin-bottom: 16px;
  }

  .preview-header h3 {
    margin: 0 0 8px 0;
  }

  .preview-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .preview-content {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
  }

  .image-preview {
    text-align: center;
  }

  .richtext-preview-full {
    line-height: 1.6;
  }
</style> 