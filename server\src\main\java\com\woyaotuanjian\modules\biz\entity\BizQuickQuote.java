package com.woyaotuanjian.modules.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 快捷报价
 * @Author: System
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Data
@TableName("biz_quick_quote")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="biz_quick_quote对象", description="快捷报价")
public class BizQuickQuote implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**
     * 报价名称
     */
    @Excel(name = "报价名称", width = 15)
    @ApiModelProperty(value = "报价名称")
    private String quoteName;
    
    /**
     * 报价明细（JSON格式）
     */
    @ApiModelProperty(value = "报价明细")
    private String quoteDetails;
    
    /**
     * 来源类型（paste/trip/file）
     */
    @Excel(name = "来源类型", width = 15)
    @ApiModelProperty(value = "来源类型")
    private String sourceType;
    
    /**
     * 原始来源内容
     */
    @ApiModelProperty(value = "原始来源内容")
    private String sourceContent;
    
    /**
     * 总金额
     */
    @Excel(name = "总金额", width = 15)
    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmount;
    
    /**
     * 人均金额
     */
    @Excel(name = "人均金额", width = 15)
    @ApiModelProperty(value = "人均金额")
    private BigDecimal perPersonAmount;
    
    /**
     * 人数
     */
    @Excel(name = "人数", width = 15)
    @ApiModelProperty(value = "人数")
    private Integer personCount;
    
    /**
     * 服务费类型（percentage/fixed）
     */
    @Excel(name = "服务费类型", width = 15)
    @ApiModelProperty(value = "服务费类型")
    private String serviceFeeType;
    
    /**
     * 服务费值
     */
    @Excel(name = "服务费值", width = 15)
    @ApiModelProperty(value = "服务费值")
    private BigDecimal serviceFeeValue;
    
    /**
     * 服务费名称
     */
    @Excel(name = "服务费名称", width = 15)
    @ApiModelProperty(value = "服务费名称")
    private String serviceFeeName;
    
    /**
     * 最后备注
     */
    @Excel(name = "最后备注", width = 30)
    @ApiModelProperty(value = "最后备注")
    private String finalRemark;
    
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
} 