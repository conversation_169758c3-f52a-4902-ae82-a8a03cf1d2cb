<template>
  <div class="quick-quote-container">
    <!-- 报价名称作为页面顶部标题 -->
    <div class="quote-name-header">
      <!-- 显示模式 -->
      <div v-if="!isEditingQuoteName" class="quote-name-input-like" 
           @dblclick="startEditQuoteName" 
           @click="handleQuoteNameClick"
           :title="isMobile ? '点击编辑' : '双击编辑'">
        <div class="quote-name-content">
          <a-icon type="file-text" class="quote-icon" />
          <span class="quote-text">{{ quoteName }}</span>
          <a-icon type="edit" class="edit-icon" @click.stop="startEditQuoteName" />
        </div>
      </div>
      <!-- 编辑模式 -->
      <a-input 
        v-else
        v-model="tempQuoteName" 
        @blur="saveQuoteName"
        @keyup.enter="saveQuoteName"
        @keyup.esc="cancelEditQuoteName"
        ref="quoteNameInput"
        placeholder="输入报价名称"
        class="quote-name-edit-input"
      />
    </div>

    <!-- 数据来源选择 -->
    <div class="source-card-wrapper">
      <a-card class="source-card modern-card" :bordered="false">
        <div class="source-section">
          <div class="section-header">
            <h3 class="section-title">
              <a-icon type="plus-circle" class="title-icon" />
              选择数据来源
            </h3>
          </div>
          <a-row :gutter="[16, 16]" class="source-buttons">
            <a-col :xs="24" :sm="12" :md="6">
              <div class="source-btn-wrapper">
                <a-button block size="large" @click="showPasteModal" class="source-btn paste-btn">
                  <div class="btn-content">
                    <a-icon type="edit" class="btn-icon" />
                    <span class="btn-text">粘贴内容</span>
                    <span class="btn-desc">从文本快速导入</span>
                  </div>
                </a-button>
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <div class="source-btn-wrapper">
                <a-button block size="large" @click="showTripModal" class="source-btn trip-btn">
                  <div class="btn-content">
                    <a-icon type="file-text" class="btn-icon" />
                    <span class="btn-text">现有行程</span>
                    <span class="btn-desc">选择已有行程</span>
                  </div>
                </a-button>
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <div class="source-btn-wrapper">
                <a-button block size="large" @click="showFileModal" class="source-btn file-btn">
                  <div class="btn-content">
                    <a-icon type="upload" class="btn-icon" />
                    <span class="btn-text">文档导入</span>
                    <span class="btn-desc">Word/PDF文档</span>
                  </div>
                </a-button>
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <div class="source-btn-wrapper">
                <a-button block size="large" @click="createBlankQuote" class="source-btn blank-btn">
                  <div class="btn-content">
                    <a-icon type="plus" class="btn-icon" />
                    <span class="btn-text">空白报价</span>
                    <span class="btn-desc">从零开始创建</span>
                  </div>
                </a-button>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 基本信息 -->
    <div v-if="hasQuoteItems" class="basic-info-wrapper">
      <a-card class="basic-info-card modern-card" :bordered="false">
        <div class="basic-info-content">
          <div class="info-header">
            <a-icon type="team" class="info-icon" />
            <span class="info-title">基本信息</span>
          </div>
          <div class="info-controls">
            <div class="person-count-section">
              <label class="count-label">人数</label>
              <div class="person-count-container">
                <a-input-number 
                  v-model="personCount" 
                  :min="1" 
                  :precision="0" 
                  size="default" 
                  class="person-count-input"
                  @change="calculateTotal" 
                  @focus="showQuickNumbers"
                  @blur="hideQuickNumbers"
                  ref="personCountInput"
                />
                <!-- 快捷人数选择 -->
                <div v-show="quickNumbersVisible" class="quick-numbers-panel">
                  <div class="quick-numbers-title">快捷选择</div>
                  <div class="quick-numbers-grid">
                    <div 
                      v-for="num in quickNumbers" 
                      :key="num"
                      class="quick-number-item"
                      :class="{ active: personCount === num }"
                      @mousedown.prevent="selectQuickNumber(num)"
                    >
                      {{ num }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <a-button type="primary" @click="updateQuantitiesByPersonCount" :loading="updating" class="update-btn">
              <a-icon type="reload" />
              更新数量
            </a-button>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 报价明细（按分类） -->
    <div v-if="hasQuoteItems" class="quote-categories">
      <div v-for="category in categories" :key="category.key" class="category-section">
        <a-card :bordered="false" class="category-card modern-card">
          <div class="category-header">
            <div class="category-title-wrapper">
              <a-icon :type="getCategoryIcon(category.key)" class="category-icon" />
              <h3 class="category-title">
                <span class="category-name">{{ category.name }}</span>
                <span v-if="getCategoryTotal(category.key) > 0" class="category-per-person">
                  {{ formatPrice(getCategoryTotal(category.key) / personCount) }}/人
                </span>
              </h3>
            </div>
          </div>

          <!-- 桌面端表格 -->
          <div class="desktop-table" v-show="!isMobile">
            <!-- 普通项目表格 -->
            <a-table v-if="category.key !== 'serviceFee'" :dataSource="categorizedItems[category.key] || []"
              :columns="quoteColumns" :pagination="false" size="middle" :rowKey="record => record.id" class="modern-table">
              <template slot="name" slot-scope="text, record">
                <a-input 
                  v-model="record.name" 
                  placeholder="输入名称" 
                  allowClear 
                  @change="calculateItemTotal(record)"
                  class="table-input"
                >
                  <a-icon 
                    v-if="canSelectFromDatabase(record.category)"
                    slot="suffix" 
                    type="database" 
                    @click="showDataSelector(record.category, record)"
                    class="desktop-db-suffix-icon"
                    title="从数据库选择"
                  />
                </a-input>
              </template>

              <template slot="unitPrice" slot-scope="text, record">
                <a-input-number 
                  v-model="record.unitPrice" 
                  :min="0" 
                  :precision="1" 
                  style="width: 100%"
                  placeholder="请输入单价"
                  @focus="$event.target.select()"
                  @change="calculateItemTotal(record)"
                  class="table-input-number" />
              </template>

              <template slot="unit" slot-scope="text, record">
                <a-select v-model="record.unit" style="width: 100%" class="table-select">
                  <a-select-option value="人">人</a-select-option>
                  <a-select-option value="间">间</a-select-option>
                  <a-select-option value="辆">辆</a-select-option>
                  <a-select-option value="团">团</a-select-option>
                  <a-select-option value="桌">桌</a-select-option>
                  <a-select-option value="船">船</a-select-option>
                  <a-select-option value="场">场</a-select-option>
                  <a-select-option value="套">套</a-select-option>
                  <a-select-option value="栋">栋</a-select-option>
                  <a-select-option value="项">项</a-select-option>
                </a-select>
              </template>

              <template slot="quantity" slot-scope="text, record">
                <a-input-number v-model="record.quantity" :min="1" :precision="0" style="width: 100%"
                  @change="calculateItemTotal(record)" class="table-input-number" />
              </template>

              <template slot="total" slot-scope="text, record">
                <div class="total-price-cell">
                  <span class="total-price">{{ formatPrice(record.total) }}</span>
                </div>
              </template>

              <template slot="remark" slot-scope="text, record">
                <a-input v-model="record.remark" placeholder="备注说明" allowClear class="table-input" />
              </template>

              <template slot="action" slot-scope="text, record">
                <a-button type="link" size="small" @click="deleteCategoryItem(category.key, record)" class="delete-btn">
                  <a-icon type="delete" />
                </a-button>
              </template>
            </a-table>

            <!-- 服务费专用表格 -->
            <a-table v-else :dataSource="categorizedItems[category.key] || []" :columns="serviceFeeColumns"
              :pagination="false" size="middle" :rowKey="record => record.id" class="modern-table">
              <template slot="name" slot-scope="text, record">
                <a-input v-model="record.name" placeholder="输入费用名称" allowClear @change="calculateServiceFeeTotal(record)" class="table-input" />
              </template>

              <template slot="serviceFeeType" slot-scope="text, record">
                <a-select v-model="record.serviceFeeType" style="width: 100%"
                  @change="calculateServiceFeeTotal(record)" class="table-select">
                  <a-select-option value="percentage">%</a-select-option>
                  <a-select-option value="fixed">元</a-select-option>
                </a-select>
              </template>

              <template slot="serviceFeeValue" slot-scope="text, record">
                <a-input-number 
                  v-model="record.serviceFeeValue" 
                  :min="0" 
                  :precision="2" 
                  style="width: 100%"
                  placeholder="请输入费用值"
                  @focus="$event.target.select()"
                  @change="calculateServiceFeeTotal(record)"
                  class="table-input-number" />
              </template>

              <template slot="unit" slot-scope="text, record">
                <a-select v-model="record.unit" style="width: 100%" class="table-select">
                  <a-select-option value="人">人</a-select-option>
                  <a-select-option value="间">间</a-select-option>
                  <a-select-option value="辆">辆</a-select-option>
                  <a-select-option value="团">团</a-select-option>
                  <a-select-option value="桌">桌</a-select-option>
                  <a-select-option value="船">船</a-select-option>
                  <a-select-option value="场">场</a-select-option>
                  <a-select-option value="套">套</a-select-option>
                  <a-select-option value="栋">栋</a-select-option>
                  <a-select-option value="项">项</a-select-option>
                </a-select>
              </template>

              <template slot="quantity" slot-scope="text, record">
                <a-input-number v-model="record.quantity" :min="0" :precision="0" style="width: 100%;"
                  @change="calculateServiceFeeTotal(record)" class="table-input-number" />
              </template>

              <template slot="total" slot-scope="text, record">
                <div class="total-price-cell">
                  <span class="total-price">{{ formatPrice(record.total) }}</span>
                </div>
              </template>

              <template slot="remark" slot-scope="text, record">
                <a-input v-model="record.remark" placeholder="备注说明" allowClear class="table-input" />
              </template>

              <template slot="action" slot-scope="text, record">
                <a-button type="link" size="small" @click="deleteCategoryItem(category.key, record)" class="delete-btn">
                  <a-icon type="delete" />
                </a-button>
              </template>
            </a-table>
            
            <!-- 桌面端添加按钮 -->
            <a-button type="dashed" block @click="addCategoryItem(category.key)" class="desktop-add-btn modern-add-btn">
              <a-icon type="plus" />
              添加{{ category.name }}
            </a-button>
          </div>

          <!-- 移动端卡片列表 -->
          <div class="mobile-cards" v-show="isMobile">
            <div v-for="item in categorizedItems[category.key] || []" :key="item.id" class="mobile-item modern-mobile-item">
              <!-- 主要信息行 -->
              <div class="mobile-item-main" @click="editMobileItem(item)">
                <div class="mobile-item-left">
                  <div class="mobile-item-name">
                    {{ item.name || category.name | truncate(15) }}
                  </div>
                  <div class="mobile-item-detail" v-if="category.key === 'serviceFee'">
                    {{ item.serviceFeeType === 'percentage' ? `${item.serviceFeeValue}%` : `${item.serviceFeeValue}元` }} × {{ item.quantity }}{{ item.unit }}
                  </div>
                  <div class="mobile-item-detail" v-else>
                    {{ item.unitPrice }}元 × {{ item.quantity }}{{ item.unit }}
                  </div>
                  <div class="mobile-item-remark" v-if="item.remark">
                    {{ item.remark | truncate(20) }}
                  </div>
                </div>
                <div class="mobile-item-right">
                  <div class="mobile-item-total">{{ formatPrice(item.total) }}</div>
                  <a-button type="link" size="small" @click.stop="deleteCategoryItem(category.key, item)" class="mobile-delete-btn">
                    <a-icon type="delete" />
                  </a-button>
                </div>
              </div>
            </div>

            <!-- 移动端添加按钮 -->
            <a-button type="dashed" block @click="addMobileCategoryItem(category.key)" class="mobile-add-btn modern-add-btn">
              <a-icon type="plus" />
              添加{{ category.name }}
            </a-button>
          </div>
        </a-card>
      </div>
    </div>

    <!-- 总计金额 -->
    <div v-if="hasQuoteItems" class="total-wrapper">
      <a-card class="total-card modern-card" :bordered="false">
        <div class="total-header">
          <a-icon type="calculator" class="total-icon" />
          <span class="total-title">费用汇总</span>
        </div>
        
        <div class="total-section">
          <div class="total-item">
            <span class="total-label">
              <a-icon type="team" />
              人数
            </span>
            <span class="total-value">{{ personCount }}人</span>
          </div>

          <div class="total-item">
            <span class="total-label">
              <a-icon type="dollar" />
              总价
            </span>
            <span class="total-value">{{ formatPrice(totalAmount) }}</span>
          </div>
          <div class="total-item per-person">
            <span class="total-label">
              <a-icon type="user" />
              人均
            </span>
            <span class="total-value">{{ formatPrice(perPersonAmount) }}</span>
          </div>
        </div>

        <!-- 最后备注 -->
        <div class="remark-section">
          <div class="remark-header">
            <a-icon type="message" class="remark-icon" />
            <h4 class="remark-title">备注</h4>
          </div>
          <a-textarea 
            v-model="finalRemark" 
            placeholder="请输入最后备注信息（如特殊说明、注意事项等）" 
            :rows="3" 
            :maxLength="500" 
            showCount 
            allowClear 
            class="modern-textarea"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
          <div class="action-buttons">
            <a-button @click="exportExcel" type="default" size="large" class="action-btn export-btn">
              <a-icon type="download" />
              导出表格
            </a-button>
            <a-button @click="copyTextVersion" type="default" size="large" class="action-btn copy-btn">
              <a-icon type="copy" />
              复制文字版
            </a-button>
            <a-button type="primary" @click="saveQuote" :loading="saving" size="large" class="action-btn save-btn">
              <a-icon type="save" />
              保存报价
            </a-button>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 原始内容显示 -->
    <div v-if="originalContent" class="original-content-wrapper">
      <a-card class="original-content-card modern-card" :bordered="false">
        <div class="content-header">
          <a-icon type="file" class="content-icon" />
          <h3 class="content-title">原始内容</h3>
        </div>
        <div class="original-content">
          <pre>{{ originalContent }}</pre>
        </div>
      </a-card>
    </div>

    <!-- 粘贴内容模态框 -->
    <a-modal title="粘贴行程内容" :visible="pasteModalVisible" @ok="handlePasteImport" @cancel="pasteModalVisible = false"
      :confirmLoading="importing" width="600px" class="modern-modal">
      <a-form-item label="行程内容">
        <a-textarea v-model="pasteContent" placeholder="请粘贴行程内容，例如：
D1：接机-景点-酒店
D2：景点-景点-酒店
D3：景点-送机" :rows="8" class="modern-textarea" />
      </a-form-item>
    </a-modal>

    <!-- 选择行程模态框 -->
    <trip-selector-modal :visible="tripModalVisible" @ok="handleTripImport" @cancel="tripModalVisible = false"
      :loading="importing" />

    <!-- 文件导入模态框 -->
    <a-modal title="文档导入" :visible="fileModalVisible" @ok="handleFileImport" @cancel="fileModalVisible = false"
      :confirmLoading="importing" width="500px" class="modern-modal">
      <a-form-item label="选择文档">
        <a-upload name="file" :fileList="fileList" :beforeUpload="beforeUpload"
          :remove="() => { fileList = []; return true; }" class="modern-upload">
          <a-button class="upload-btn">
            <a-icon type="upload" /> 选择文件
          </a-button>
          <span style="margin-left: 8px; color: rgba(0,0,0,.45);">支持格式：Word、PDF文件</span>
        </a-upload>
      </a-form-item>
    </a-modal>

    <!-- 数据选择器模态框 -->
    <data-selector-modal
      :visible="dataSelectorVisible"
      :dataType="dataSelectorType"
      @ok="handleDataSelectorOk"
      @cancel="dataSelectorVisible = false"
    />

    <!-- 移动端编辑弹窗 -->
    <a-modal 
      title="编辑项目" 
      :visible="mobileEditModalVisible" 
      @ok="saveMobileEdit" 
      @cancel="cancelMobileEdit"
      :width="320"
      :bodyStyle="{ padding: '16px' }"
      okText="保存"
      cancelText="取消"
      class="modern-modal mobile-edit-modal"
    >
      <div v-if="editingItem">
        <a-form-item label="名称" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
          <a-input 
            v-model="editingItem.name" 
            placeholder="输入名称" 
            allowClear
            class="modern-input"
          >
            <a-icon 
              v-if="canSelectFromDatabase(editingItem.category)"
              slot="suffix" 
              type="database" 
              @click="showDataSelectorForMobile(editingItem.category)"
              class="mobile-db-suffix-icon"
              :title="`从数据库选择${editingItem.category === 'scenic' ? '景点' : '酒店'}`"
            />
          </a-input>
        </a-form-item>

        <!-- 服务费特殊字段 -->
        <template v-if="editingItem.category === 'serviceFee'">
          <a-form-item label="计费方式" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-select v-model="editingItem.serviceFeeType" class="modern-select">
              <a-select-option value="percentage">%</a-select-option>
              <a-select-option value="fixed">元</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="费用值" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-input-number 
              v-model="editingItem.serviceFeeValue" 
              :min="0" 
              :precision="2" 
              style="width: 100%" 
              placeholder="请输入费用值"
              @focus="$event.target.select()"
              class="modern-input-number" />
          </a-form-item>
        </template>

        <!-- 普通项目字段 -->
        <template v-else>
          <a-form-item label="单价" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-input-number 
              v-model="editingItem.unitPrice" 
              :min="0" 
              :precision="1" 
              style="width: 100%" 
              placeholder="请输入单价"
              @focus="$event.target.select()"
              class="modern-input-number" />
          </a-form-item>
        </template>

        <a-form-item label="数量" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
          <a-input-number v-model="editingItem.quantity" :min="1" :precision="0" style="width: 100%" class="modern-input-number" />
        </a-form-item>

        <a-form-item label="单位" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
          <a-select v-model="editingItem.unit" class="modern-select">
            <a-select-option value="人">人</a-select-option>
            <a-select-option value="间">间</a-select-option>
            <a-select-option value="辆">辆</a-select-option>
            <a-select-option value="团">团</a-select-option>
            <a-select-option value="桌">桌</a-select-option>
            <a-select-option value="船">船</a-select-option>
            <a-select-option value="场">场</a-select-option>
            <a-select-option value="套">套</a-select-option>
            <a-select-option value="栋">栋</a-select-option>
            <a-select-option value="项">项</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="备注" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
          <a-textarea v-model="editingItem.remark" placeholder="备注说明" :rows="2" allowClear class="modern-textarea" />
        </a-form-item>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
import moment from 'moment'
import { getAction, postAction, putAction } from '@/api/manage'
import TripSelectorModal from './modules/TripSelectorModal.vue'
import DataSelectorModal from './modules/DataSelectorModal.vue'

export default {
  name: 'QuickQuote',
  
  inject: ['closeCurrent', 'onCloseCurrent'],
  
  components: {
    TripSelectorModal,
    DataSelectorModal
  },

  filters: {
    // 文本截断过滤器
    truncate(text, length = 10) {
      if (!text) return ''
      if (text.length <= length) return text
      return text.substring(0, length) + '...'
    }
  },

  data() {
    // 生成默认报价名称
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hour = String(now.getHours()).padStart(2, '0')
    const minute = String(now.getMinutes()).padStart(2, '0')
    const defaultQuoteName = `快捷报价-${year}${month}${day}${hour}${minute}`
    
    return {
      // 基本信息
      quoteName: defaultQuoteName,
      personCount: 10,
      finalRemark: '',

      // 分类定义
      categories: [
        { key: 'scenic', name: '景点' },
        { key: 'hotel', name: '酒店' },
        { key: 'restaurant', name: '餐费' },
        { key: 'traffic', name: '交通' },
        { key: 'guide', name: '导游' },
        { key: 'other', name: '其他' },
        { key: 'serviceFee', name: '操作费|服务费' }
      ],

      // 分类报价项目
      categorizedItems: {
        scenic: [],
        hotel: [],
        restaurant: [],
        traffic: [],
        guide: [],
        other: [],
        serviceFee: []
      },

      originalContent: '',

      // 模态框状态
      pasteModalVisible: false,
      tripModalVisible: false,
      fileModalVisible: false,
      tempQuoteName: '',
      isEditingQuoteName: false,

      // 导入相关
      pasteContent: '',
      fileList: [],
      importing: false,
      saving: false,
      updating: false,
      loading: false,

      // 响应式
      isMobile: false,

      // 表格列定义
      quoteColumns: [
        {
          title: '名称',
          dataIndex: 'name',
          width: '20%',
          scopedSlots: { customRender: 'name' }
        },
        {
          title: '单价',
          dataIndex: 'unitPrice',
          width: '15%',
          scopedSlots: { customRender: 'unitPrice' }
        },
        {
          title: '单位',
          dataIndex: 'unit',
          width: '10%',
          scopedSlots: { customRender: 'unit' }
        },
        {
          title: '数量',
          dataIndex: 'quantity',
          width: '10%',
          scopedSlots: { customRender: 'quantity' }
        },
        {
          title: '小计',
          dataIndex: 'total',
          width: '15%',
          scopedSlots: { customRender: 'total' }
        },
        {
          title: '备注',
          dataIndex: 'remark',
          width: '20%',
          scopedSlots: { customRender: 'remark' }
        },
        {
          title: '操作',
          width: '10%',
          scopedSlots: { customRender: 'action' }
        }
      ],

      // 服务费专用表格列
      serviceFeeColumns: [
        {
          title: '费用名称',
          dataIndex: 'name',
          width: '18%',
          scopedSlots: { customRender: 'name' }
        },
        {
          title: '计费方式',
          dataIndex: 'serviceFeeType',
          width: '12%',
          scopedSlots: { customRender: 'serviceFeeType' }
        },
        {
          title: '费用值',
          dataIndex: 'serviceFeeValue',
          width: '12%',
          scopedSlots: { customRender: 'serviceFeeValue' }
        },
        {
          title: '单位',
          dataIndex: 'unit',
          width: '10%',
          scopedSlots: { customRender: 'unit' }
        },
        {
          title: '数量',
          dataIndex: 'quantity',
          width: '10%',
          scopedSlots: { customRender: 'quantity' }
        },
        {
          title: '小计',
          dataIndex: 'total',
          width: '13%',
          scopedSlots: { customRender: 'total' }
        },
        {
          title: '备注',
          dataIndex: 'remark',
          width: '15%',
          scopedSlots: { customRender: 'remark' }
        },
        {
          title: '操作',
          width: '10%',
          scopedSlots: { customRender: 'action' }
        }
      ],

      // 移动端编辑相关
      mobileEditModalVisible: false,
      editingItem: null,

      // 快捷人数选择
      quickNumbersVisible: false,
      quickNumbers: [4, 6, 10, 20, 30, 40, 50],

      // 数据选择器相关
      dataSelectorVisible: false,
      dataSelectorType: 'scenic', // scenic, hotel
      currentCategoryForSelector: null,
      currentEditingRecord: null // 当前编辑的记录（桌面端）
    }
  },

  computed: {
    // 是否有报价项目
    hasQuoteItems() {
      return this.categories.some(category => 
        this.categorizedItems[category.key] && this.categorizedItems[category.key].length > 0
      )
    },

    // 所有报价项目的数组
    allQuoteItems() {
      let items = []
      this.categories.forEach(category => {
        if (this.categorizedItems[category.key]) {
          items = items.concat(this.categorizedItems[category.key])
        }
      })
      return items
    },

    // 总金额
    totalAmount() {
      return this.allQuoteItems.reduce((sum, item) => sum + (item.total || 0), 0)
    },

    // 人均金额
    perPersonAmount() {
      if (this.personCount <= 0) return 0
      return this.totalAmount / this.personCount
    }
  },

  mounted() {
    this.checkMobile()
    window.addEventListener('resize', this.checkMobile)
    // 检查是否是编辑模式
    this.checkEditMode()
    // 组件挂载后，确保百分比服务费计算正确
    this.$nextTick(() => {
      this.updatePercentageServiceFees()
    })
  },

  async created() {
    // 注册页面关闭时的重置回调
    this.onCloseCurrent(this.$route.fullPath, () => {
      this.resetForm()
    })
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.checkMobile)
  },

  watch: {
    // 监听路由变化，重新检查编辑模式
    '$route'(to, from) {
      // 如果路由的 query 参数发生变化，重新检查编辑模式
      if (to.query.id !== from.query.id) {
        this.checkEditMode()
      }
    }
  },

  methods: {
    // 检测是否为移动设备
    checkMobile() {
      this.isMobile = window.innerWidth <= 768
    },

    // 检查是否是编辑模式
    async checkEditMode() {
      const quoteId = this.$route.query.id
      if (quoteId) {
        await this.loadQuoteData(quoteId)
      } else {
        // 如果没有 ID，说明是新建模式，重置表单
        this.resetForm()
      }
    },

    // 加载报价数据
    async loadQuoteData(quoteId) {
      try {
        this.loading = true
        const response = await getAction('/biz/bizQuickQuote/queryById', { id: quoteId })
        
        if (response.success && response.result) {
          const quoteData = response.result
          
          // 设置基本信息
          this.quoteName = quoteData.quoteName || this.quoteName
          this.personCount = quoteData.personCount || 1
          this.finalRemark = quoteData.finalRemark || ''
          this.originalContent = quoteData.sourceContent || ''
          
          // 解析报价明细
          if (quoteData.quoteDetails) {
            try {
              const quoteDetails = JSON.parse(quoteData.quoteDetails)
              
              // 支持新的分类结构
              if (quoteDetails.categorizedItems) {
                this.categorizedItems = quoteDetails.categorizedItems
              } else if (quoteDetails.items && Array.isArray(quoteDetails.items)) {
                // 兼容旧的数据格式，将旧格式转换为新格式
                this.migrateOldDataFormat(quoteDetails.items)
              }
              
              // 确保所有分类都存在
              this.categories.forEach(category => {
                if (!this.categorizedItems[category.key]) {
                  this.$set(this.categorizedItems, category.key, [])
                }
              })
              
              // 重新计算所有金额
              this.$nextTick(() => {
                this.updatePercentageServiceFees()
                this.calculateTotal()
              })
              
            } catch (error) {
              console.error('解析报价明细失败:', error)
              this.$message.error('报价数据格式错误')
            }
          }
          
          this.$message.success('报价数据加载成功')
        } else {
          this.$message.error(response.message || '加载报价数据失败')
        }
      } catch (error) {
        console.error('加载报价数据失败:', error)
        this.$message.error('加载报价数据失败')
      } finally {
        this.loading = false
      }
    },

    // 迁移旧数据格式
    migrateOldDataFormat(oldItems) {
      // 清空分类项目
      this.categories.forEach(category => {
        this.categorizedItems[category.key] = []
      })
      
      // 将旧格式的项目分配到对应分类
      oldItems.forEach(item => {
        const categoryKey = this.mapBizTypeToCategory(item.bizType) || 'other'
        if (!this.categorizedItems[categoryKey]) {
          this.$set(this.categorizedItems, categoryKey, [])
        }
        
        // 为旧项目添加分类信息
        item.category = categoryKey
        this.categorizedItems[categoryKey].push(item)
      })
    },

    // 映射业务类型到分类
    mapBizTypeToCategory(bizType) {
      switch (bizType) {
        case 'scenic':
          return 'scenic'
        case 'hotel':
          return 'hotel'
        case 'rest':
          return 'restaurant'
        case 'traffic':
          return 'traffic'
        case 'commonFee':
          return 'guide'
        case 'serviceFee':
          return 'serviceFee'
        default:
          return 'other'
      }
    },

    // 处理报价名称点击事件
    handleQuoteNameClick() {
      if (this.isMobile) {
        // 移动端单击即可编辑
        this.startEditQuoteName()
      }
      // 桌面端需要双击，所以这里不做处理
    },

    // 开始编辑报价名称
    startEditQuoteName() {
      this.tempQuoteName = this.quoteName
      this.isEditingQuoteName = true
      this.$nextTick(() => {
        if (this.$refs.quoteNameInput) {
          this.$refs.quoteNameInput.focus()
          if (!this.isMobile) {
            // 桌面端选中所有文本，移动端不选中（避免虚拟键盘问题）
            this.$refs.quoteNameInput.select()
          }
        }
      })
    },

    // 保存报价名称
    saveQuoteName() {
      if (!this.tempQuoteName.trim()) {
        this.$message.error('请输入报价名称')
        this.cancelEditQuoteName()
        return
      }
      this.quoteName = this.tempQuoteName.trim()
      this.isEditingQuoteName = false
      this.$message.success('报价名称已更新')
    },

    // 取消编辑报价名称
    cancelEditQuoteName() {
      this.tempQuoteName = ''
      this.isEditingQuoteName = false
    },

    // 显示粘贴模态框
    showPasteModal() {
      this.pasteModalVisible = true
      this.pasteContent = ''
    },

    // 显示行程选择模态框
    showTripModal() {
      this.tripModalVisible = true
    },

    // 显示文件导入模态框
    showFileModal() {
      this.fileModalVisible = true
      this.fileList = []
    },

    // 创建空白报价
    createBlankQuote() {
      // 初始化所有分类项目为空
      this.categorizedItems = {
        scenic: [],
        hotel: [],
        restaurant: [],
        traffic: [],
        guide: [],
        other: [],
        serviceFee: []
      }

      // 添加基础项目（餐费、租车、导游）
      this.addCategoryItem('restaurant')
      this.addCategoryItem('traffic')
      this.addCategoryItem('guide')

      this.$message.success('已创建空白报价模板')
    },

    // 处理粘贴导入
    async handlePasteImport() {
      if (!this.pasteContent.trim()) {
        this.$message.error('请输入要解析的内容')
        return
      }

      this.importing = true
      try {
        const response = await postAction('/biz/bizQuickQuote/parsePastedContent', {
          content: this.pasteContent
        })

        if (response.success) {
          this.categorizedItems = response.result.categorizedItems || {}
          this.quoteName = response.result.quoteName || this.quoteName
          this.originalContent = response.result.originalContent || this.pasteContent
          // 确保餐费只有一个项目
          this.ensureOnlyOneRestaurantItem()
          // 导入完成后，重新计算所有百分比服务费
          this.updatePercentageServiceFees()
          this.pasteModalVisible = false
          this.$message.success('导入成功')
        } else {
          this.$message.error(response.message || '导入失败')
        }
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('导入失败')
      } finally {
        this.importing = false
      }
    },

    // 处理行程导入
    async handleTripImport(tripId) {
      this.importing = true
      try {
        const response = await postAction('/biz/bizQuickQuote/importFromTrip', {
          tripId: tripId
        })

        if (response.success) {
          this.categorizedItems = response.result.categorizedItems || {}
          this.quoteName = response.result.quoteName || this.quoteName
          // 确保餐费只有一个项目
          this.ensureOnlyOneRestaurantItem()
          // 导入完成后，重新计算所有百分比服务费
          this.updatePercentageServiceFees()
          this.tripModalVisible = false
          this.$message.success('导入成功')
        } else {
          this.$message.error(response.message || '导入失败')
        }
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('导入失败')
      } finally {
        this.importing = false
      }
    },

    // 处理文件导入
    async handleFileImport() {
      if (this.fileList.length === 0) {
        this.$message.error('请选择要导入的文件')
        return
      }

      this.importing = true
      try {
        const formData = new FormData()
        formData.append('file', this.fileList[0])

        const response = await postAction('/biz/bizQuickQuote/importFromFile', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response.success) {
          this.categorizedItems = response.result.categorizedItems || {}
          this.quoteName = response.result.quoteName || this.quoteName
          // 确保餐费只有一个项目
          this.ensureOnlyOneRestaurantItem()
          // 导入完成后，重新计算所有百分比服务费
          this.updatePercentageServiceFees()
          this.fileModalVisible = false
          this.$message.success('导入成功')
        } else {
          this.$message.error(response.message || '导入失败')
        }
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('导入失败')
      } finally {
        this.importing = false
      }
    },

    // 文件上传前处理
    beforeUpload(file) {
      this.fileList = [file]
      return false // 阻止自动上传
    },

    // 添加分类项目
    addCategoryItem(categoryKey) {
      let newItem
      
      if (categoryKey === 'serviceFee') {
        // 服务费特殊处理
        newItem = {
          id: uuidv4(),
          name: '操作费',
          category: categoryKey,
          serviceFeeType: 'percentage',
          serviceFeeValue: null,
          unit: '项',
          quantity: 1,
          total: 0,
          remark: '',
          bizType: 'serviceFee',
          bizId: 0
        }
      } else {
        // 普通项目 - 根据分类设置默认数量
        const defaultQuantity = this.getDefaultQuantityByCategory(categoryKey)
        newItem = {
          id: uuidv4(),
          name: this.getDefaultNameByCategory(categoryKey),
          category: categoryKey,
          unitPrice: null,
          unit: this.getDefaultUnitByCategory(categoryKey),
          quantity: defaultQuantity,
          total: 0,
          remark: '',
          bizType: this.mapCategoryToBizType(categoryKey),
          bizId: 0
        }
      }
      
      if (!this.categorizedItems[categoryKey]) {
        this.$set(this.categorizedItems, categoryKey, [])
      }
      this.categorizedItems[categoryKey].push(newItem)
    },

    // 删除分类项目
    deleteCategoryItem(categoryKey, record) {
      const items = this.categorizedItems[categoryKey]
      if (items) {
        const index = items.findIndex(item => item.id === record.id)
        if (index > -1) {
          items.splice(index, 1)
          // 如果删除的是普通项目，需要联动更新百分比服务费
          if (categoryKey !== 'serviceFee') {
            this.updatePercentageServiceFees()
          }
          this.calculateTotal()
        }
      }
    },

    // 计算单项总价
    calculateItemTotal(item) {
      if (item.category === 'serviceFee') {
        // 服务费特殊计算
        this.calculateServiceFeeTotal(item)
      } else {
        // 普通项目计算 - 处理null值
        if (!item.unitPrice || item.unitPrice <= 0 || !item.quantity || item.quantity <= 0) {
          item.total = 0
        } else {
          item.total = Number((item.unitPrice * item.quantity).toFixed(1))
        }
        // 普通项目更新后，联动更新所有百分比类型的服务费
        this.updatePercentageServiceFees()
      }
      this.calculateTotal()
    },

    // 计算服务费总价
    calculateServiceFeeTotal(item, skipPercentageUpdate = false) {
      if (!item.serviceFeeValue || item.serviceFeeValue <= 0 || !item.quantity || item.quantity <= 0) {
        item.total = 0
        this.calculateTotal()
        return
      }
      
      let unitTotal = 0
      if (item.serviceFeeType === 'percentage') {
        // 按百分比计算，基于除服务费外的其他费用小计
        const otherSubtotal = this.allQuoteItems
          .filter(i => i.category !== 'serviceFee')
          .reduce((sum, i) => sum + (i.total || 0), 0)
        unitTotal = otherSubtotal * (item.serviceFeeValue / 100)
      } else {
        // 固定金额
        unitTotal = item.serviceFeeValue
        // 固定金额的服务费变更时，需要联动更新百分比服务费
        if (!skipPercentageUpdate) {
          this.updatePercentageServiceFees()
        }
      }
      
      // 乘以数量
      item.total = Number((unitTotal * item.quantity).toFixed(1))
      
      this.calculateTotal()
    },

    // 更新所有百分比类型的服务费
    updatePercentageServiceFees() {
      const serviceFeeItems = this.categorizedItems.serviceFee || []
      serviceFeeItems.forEach(item => {
        if (item.serviceFeeType === 'percentage') {
          // 传入 skipPercentageUpdate = true 避免无限循环
          this.calculateServiceFeeTotal(item, true)
        }
      })
    },

    // 计算总价
    calculateTotal() {
      // 触发计算属性更新
      this.$forceUpdate()
    },

    // 根据人数更新数量（前端计算）
    updateQuantitiesByPersonCount() {
      if (this.personCount <= 0) {
        this.$message.error('请输入有效的人数')
        return
      }

      this.updating = true
      
      try {
        // 计算房间数（人数除以2上取整）
        const roomCount = Math.ceil(this.personCount / 2)
        
        // 更新各分类的数量
        this.updateCategoryQuantities('scenic', this.personCount, '人')
        this.updateCategoryQuantities('hotel', roomCount, '间')
        this.updateCategoryQuantities('restaurant', this.personCount, '人')
        this.updateCategoryQuantities('traffic', 1, '辆')
        this.updateCategoryQuantities('guide', 1, '人')
        this.updateCategoryQuantities('other', this.personCount, '项')
        this.updateCategoryQuantities('serviceFee', 1, '项')
        
        // 更新完成后，重新计算所有百分比服务费
        this.updatePercentageServiceFees()
        
        this.$message.success('数量已更新')
      } catch (error) {
        console.error('更新数量失败:', error)
        this.$message.error('更新失败')
      } finally {
        this.updating = false
      }
    },

    // 更新分类中所有项目的数量
    updateCategoryQuantities(categoryKey, quantity, unit) {
      const items = this.categorizedItems[categoryKey]
      if (!items) return
      
      items.forEach(item => {
        item.quantity = quantity
        // 如果单位为空或者是默认单位，则更新单位
        if (!item.unit || item.unit === '') {
          item.unit = unit
        }
        // 重新计算总价
        if (categoryKey === 'serviceFee') {
          this.calculateServiceFeeTotal(item)
        } else {
          this.calculateItemTotal(item)
        }
      })
    },

    // 格式化价格显示
    formatPrice(price) {
      return `￥${(price || 0).toFixed(1)}`
    },

    // 导出Excel
    exportExcel() {
      import('xlsx').then(XLSX => {
        const wb = XLSX.utils.book_new()
        const exportData = []

        // 添加报价信息
        exportData.push([this.quoteName || '快捷报价'])
        exportData.push(['人数', this.personCount])
        exportData.push(['生成时间', moment().format('YYYY-MM-DD HH:mm:ss')])
        exportData.push([]) // 空行

        // 按分类添加明细
        this.categories.forEach(category => {
          const items = this.categorizedItems[category.key] || []
          if (items.length > 0) {
            exportData.push([`【${category.name}】`])
            
            if (category.key === 'serviceFee') {
              // 服务费特殊格式
              exportData.push(['费用名称', '计费方式', '费用值', '单位', '数量', '小计', '备注'])
              items.forEach(item => {
                const feeTypeText = item.serviceFeeType === 'percentage' ? '百分比' : '固定金额'
                const feeValueText = item.serviceFeeType === 'percentage' 
                  ? `${item.serviceFeeValue}%` 
                  : `${item.serviceFeeValue}元`
                exportData.push([
                  item.name,
                  feeTypeText,
                  feeValueText,
                  item.unit,
                  item.quantity,
                  item.total,
                  item.remark
                ])
              })
            } else {
              // 普通项目格式
              exportData.push(['名称', '单价', '单位', '数量', '小计', '备注'])
              items.forEach(item => {
                exportData.push([
                  item.name,
                  item.unitPrice,
                  item.unit,
                  item.quantity,
                  item.total,
                  item.remark
                ])
              })
            }
            exportData.push([]) // 空行
          }
        })

        // 添加总计
        exportData.push(['总价', '', '', '', this.totalAmount, ''])
        exportData.push(['人均', '', '', '', this.perPersonAmount, ''])

        // 添加最后备注
        if (this.finalRemark && this.finalRemark.trim()) {
          exportData.push([]) // 空行
          exportData.push(['备注', this.finalRemark, '', '', '', ''])
        }

        const ws = XLSX.utils.aoa_to_sheet(exportData)
        XLSX.utils.book_append_sheet(wb, ws, '报价明细')

        const fileName = `${this.quoteName || '快捷报价'}_${moment().format('YYYYMMDDHHmmss')}.xlsx`
        XLSX.writeFile(wb, fileName)
      })
    },

    // 复制文字版
    copyTextVersion() {
      let textContent = `📋 ${this.quoteName || '报价'}\n`
      textContent += '═══════════════════════════\n'

      // 按分类输出
      this.categories.forEach(category => {
        const items = this.categorizedItems[category.key] || []
        if (items.length > 0) {
          const categoryTotal = this.getCategoryTotal(category.key)
          const categoryPerPerson = categoryTotal / this.personCount
          
          // 输出分类标题
          textContent += `【${category.name}】\n`
          
          items.forEach(item => {
            if (item.name && item.total > 0) {
              if (category.key === 'serviceFee') {
                // 服务费特殊格式
                const feeText = item.serviceFeeType === 'percentage' 
                  ? `${item.serviceFeeValue}%` 
                  : `${item.serviceFeeValue}元`
                textContent += `• ${item.name} ${feeText}×${item.quantity}${item.unit} = ${this.formatPrice(item.total)}\n`
              } else {
                // 普通项目格式
                textContent += `• ${item.name} ${item.unitPrice}元×${item.quantity}${item.unit} = ${this.formatPrice(item.total)}\n`
              }
              
              // 如果有备注，添加到项目下面
              if (item.remark && item.remark.trim()) {
                textContent += `  💬 ${item.remark}\n`
              }
            }
          })
          
          // 在分类项目最后添加小计
          textContent += `△ 小计：${this.formatPrice(categoryPerPerson)}/人\n\n`
        }
      })
      
      textContent += '═══════════════════════════\n'
      textContent += `👥 人数：${this.personCount}人\n`
      textContent += `💰 总价：${this.formatPrice(this.totalAmount)}\n`
      textContent += `💳 人均：${this.formatPrice(this.perPersonAmount)}\n`
      
      if (this.finalRemark && this.finalRemark.trim()) {
        textContent += '───────────────────────────\n'
        textContent += `📝 备注：${this.finalRemark}\n`
      }

      // 复制到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(textContent).then(() => {
          this.$message.success('已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyText(textContent)
        })
      } else {
        this.fallbackCopyText(textContent)
      }
    },

    // 兜底复制方法
    fallbackCopyText(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        this.$message.success('已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
        console.error('复制失败:', err)
      }
      document.body.removeChild(textArea)
    },

    // 保存报价
    async saveQuote() {
      if (!this.quoteName.trim()) {
        this.$message.error('请输入报价名称')
        return
      }

      // 验证报价数据
      const validationErrors = this.validateQuoteData()
      if (validationErrors.length > 0) {
        // 显示第一个错误
        this.$message.error(validationErrors[0])
        return
      }

      this.saving = true
      try {
        const quoteData = {
          quoteName: this.quoteName,
          quoteDetails: JSON.stringify({
            categorizedItems: this.categorizedItems,
            personCount: this.personCount
          }),
          sourceType: this.originalContent ? 'paste' : 'manual',
          sourceContent: this.originalContent,
          totalAmount: this.totalAmount,
          perPersonAmount: this.perPersonAmount,
          personCount: this.personCount,
          finalRemark: this.finalRemark
        }

        // 检查是否是编辑模式
        const quoteId = this.$route.query.id
        let response
        
        if (quoteId) {
          // 编辑模式：更新现有报价
          quoteData.id = quoteId
          response = await putAction('/biz/bizQuickQuote/edit', quoteData)
        } else {
          // 新建模式：创建新报价
          response = await postAction('/biz/bizQuickQuote/add', quoteData)
        }

        if (response.success) {
          this.$message.success(quoteId ? '更新成功' : '保存成功')
          // 关闭当前页面
          setTimeout(() => {
            this.closeCurrent()
          }, 1000)
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.saving = false
      }
    },

    // 验证报价数据
    validateQuoteData() {
      const errors = []
      
      // 检查是否有报价项目
      if (!this.hasQuoteItems) {
        errors.push('请添加报价项目')
        return errors
      }
      
      // 检查每个分类的项目
      this.categories.forEach(category => {
        const items = this.categorizedItems[category.key] || []
        items.forEach((item, index) => {
          // 检查名称
          if (!item.name || !item.name.trim()) {
            errors.push(`${category.name}第${index + 1}项：请填写名称`)
          }
          
          // 检查单价或服务费值
          if (category.key === 'serviceFee') {
            // 服务费值可以是0，但不能为空
            if (item.serviceFeeValue === null || item.serviceFeeValue === undefined || item.serviceFeeValue === '') {
              errors.push(`${category.name}第${index + 1}项：请填写费用值`)
            }
          } else {
            // 单价可以是0，但不能为空
            if (item.unitPrice === null || item.unitPrice === undefined || item.unitPrice === '') {
              errors.push(`${category.name}第${index + 1}项：请填写单价`)
            }
          }
          
          // 检查数量 - 数量可以是0，但不能为空，且不能小于0
          if (item.quantity === null || item.quantity === undefined || item.quantity === '' || item.quantity < 0) {
            errors.push(`${category.name}第${index + 1}项：请填写数量`)
          }
        })
      })
      
      return errors
    },

    // 根据分类获取默认单位
    getDefaultUnitByCategory(category) {
      switch (category) {
        case 'scenic':
          return '人'
        case 'hotel':
          return '间'
        case 'restaurant':
          return '人'
        case 'traffic':
          return '辆'
        case 'guide':
          return '人'
        default:
          return '项'
      }
    },

    // 根据分类获取默认数量
    getDefaultQuantityByCategory(category) {
      switch (category) {
        case 'scenic':
          return this.personCount || 1
        case 'hotel':
          return Math.ceil((this.personCount || 1) / 2)
        case 'restaurant':
          return this.personCount || 1
        case 'traffic':
          return 1
        case 'guide':
          return 1
        case 'other':
          return this.personCount || 1
        default:
          return 1
      }
    },

    // 根据分类获取默认名称
    getDefaultNameByCategory(category) {
      switch (category) {
        case 'scenic':
          return '门票'
        case 'hotel':
          return '酒店'
        case 'restaurant':
          return '餐费'
        case 'traffic':
          return '租车'
        case 'guide':
          return '导游'
        case 'other':
          return '其他'
        default:
          return '费用项目'
      }
    },

    // 映射分类到业务类型
    mapCategoryToBizType(category) {
      switch (category) {
        case 'scenic':
          return 'scenic'
        case 'hotel':
          return 'hotel'
        case 'restaurant':
          return 'rest'
        case 'traffic':
          return 'traffic'
        case 'guide':
          return 'commonFee'
        case 'serviceFee':
          return 'serviceFee'
        default:
          return 'other'
      }
    },

    // 编辑移动端项目
    editMobileItem(item) {
      this.editingItem = { ...item }
      this.mobileEditModalVisible = true
    },

    // 移动端添加分类项目
    addMobileCategoryItem(categoryKey) {
      let newItem
      
      if (categoryKey === 'serviceFee') {
        // 服务费特殊处理
        newItem = {
          id: uuidv4(),
          name: '操作费',
          category: categoryKey,
          serviceFeeType: 'percentage',
          serviceFeeValue: null,
          unit: '项',
          quantity: 1,
          total: 0,
          remark: '',
          bizType: 'serviceFee',
          bizId: 0
        }
      } else {
        // 普通项目 - 根据分类设置默认数量
        const defaultQuantity = this.getDefaultQuantityByCategory(categoryKey)
        newItem = {
          id: uuidv4(),
          name: this.getDefaultNameByCategory(categoryKey),
          category: categoryKey,
          unitPrice: null,
          unit: this.getDefaultUnitByCategory(categoryKey),
          quantity: defaultQuantity,
          total: 0,
          remark: '',
          bizType: this.mapCategoryToBizType(categoryKey),
          bizId: 0
        }
      }
      
      // 直接弹出编辑对话框
      this.editingItem = newItem
      this.mobileEditModalVisible = true
    },

    // 保存移动端编辑
    saveMobileEdit() {
      if (this.editingItem) {
        // 验证必填字段
        if (!this.editingItem.name || !this.editingItem.name.trim()) {
          this.$message.error('请填写名称')
          return
        }
        
        if (this.editingItem.category === 'serviceFee') {
          if (!this.editingItem.serviceFeeValue || this.editingItem.serviceFeeValue <= 0) {
            this.$message.error('请填写费用值')
            return
          }
        } else {
          if (!this.editingItem.unitPrice || this.editingItem.unitPrice <= 0) {
            this.$message.error('请填写单价')
            return
          }
        }
        
        if (!this.editingItem.quantity || this.editingItem.quantity <= 0) {
          this.$message.error('请填写数量')
          return
        }
        
        const categoryKey = this.editingItem.category
        
        // 确保分类数组存在
        if (!this.categorizedItems[categoryKey]) {
          this.$set(this.categorizedItems, categoryKey, [])
        }
        
        const items = this.categorizedItems[categoryKey]
        const index = items.findIndex(i => i.id === this.editingItem.id)
        
        if (index > -1) {
          // 更新现有项目数据
          this.$set(this.categorizedItems[categoryKey], index, { ...this.editingItem })
          // 重新计算总价
          if (categoryKey === 'serviceFee') {
            this.calculateServiceFeeTotal(this.categorizedItems[categoryKey][index])
          } else {
            this.calculateItemTotal(this.categorizedItems[categoryKey][index])
          }
        } else {
          // 新增项目
          const newItem = { ...this.editingItem }
          this.categorizedItems[categoryKey].push(newItem)
          // 重新计算总价
          if (categoryKey === 'serviceFee') {
            this.calculateServiceFeeTotal(newItem)
          } else {
            this.calculateItemTotal(newItem)
          }
        }
      }
      this.mobileEditModalVisible = false
      this.editingItem = null
    },

    // 取消移动端编辑
    cancelMobileEdit() {
      this.mobileEditModalVisible = false
      this.editingItem = null
    },

    // 确保餐费只有一个标准项目（直接创建标准餐费项目）
    ensureOnlyOneRestaurantItem() {
      // 清空现有餐费项目，重新创建一个标准的餐费项目
      this.categorizedItems.restaurant = []
      this.addCategoryItem('restaurant')
    },

    // 显示快捷人数选择
    showQuickNumbers() {
      this.quickNumbersVisible = true
    },

    // 隐藏快捷人数选择
    hideQuickNumbers() {
      // 延迟隐藏，确保点击事件能够触发
      setTimeout(() => {
        this.quickNumbersVisible = false
      }, 200)
    },

    // 选择快捷人数
    selectQuickNumber(num) {
      this.personCount = num
      this.quickNumbersVisible = false
      this.calculateTotal()
      // 重新聚焦输入框
      this.$nextTick(() => {
        if (this.$refs.personCountInput) {
          this.$refs.personCountInput.focus()
        }
      })
    },

    // 判断是否可以从数据库选择
    canSelectFromDatabase(category) {
      return ['scenic', 'hotel'].includes(category)
    },

    // 显示数据选择器（桌面端）
    showDataSelector(category, record = null) {
      this.currentCategoryForSelector = category
      this.currentEditingRecord = record // 保存当前编辑的记录
      this.dataSelectorType = category
      this.dataSelectorVisible = true
    },

    // 显示数据选择器（移动端）
    showDataSelectorForMobile(category) {
      this.currentCategoryForSelector = category
      this.dataSelectorType = category
      this.dataSelectorVisible = true
    },

    // 处理数据选择器确认
    handleDataSelectorOk(selectedData) {
      if (!selectedData || !this.currentCategoryForSelector) {
        this.dataSelectorVisible = false
        return
      }

      // 如果是移动端编辑模式
      if (this.mobileEditModalVisible && this.editingItem) {
        // 更新编辑项目的数据
        this.editingItem.name = selectedData.name
        this.editingItem.bizId = selectedData.id
        this.editingItem.bizType = selectedData.bizType
        
        // 如果有价格信息，也更新价格
        if (selectedData.price) {
          if (this.editingItem.category === 'serviceFee') {
            this.editingItem.serviceFeeValue = selectedData.price
          } else {
            this.editingItem.unitPrice = selectedData.price
          }
        }
        
        // 如果有价格备注，也更新备注
        if (selectedData.priceRemark) {
          this.editingItem.remark = selectedData.priceRemark
        }
      } else if (this.currentEditingRecord) {
        // 桌面端编辑模式：更新当前记录
        this.currentEditingRecord.name = selectedData.name
        this.currentEditingRecord.bizId = selectedData.id
        this.currentEditingRecord.bizType = selectedData.bizType
        
        // 如果有价格信息，也更新价格
        if (selectedData.price) {
          if (this.currentEditingRecord.category === 'serviceFee') {
            this.currentEditingRecord.serviceFeeValue = selectedData.price
          } else {
            this.currentEditingRecord.unitPrice = selectedData.price
          }
        }
        
        // 如果有价格备注，也更新备注
        if (selectedData.priceRemark) {
          this.currentEditingRecord.remark = selectedData.priceRemark
        }
        
        // 重新计算总价
        if (this.currentEditingRecord.category === 'serviceFee') {
          this.calculateServiceFeeTotal(this.currentEditingRecord)
        } else {
          this.calculateItemTotal(this.currentEditingRecord)
        }
      } else {
        // 桌面端模式：为当前分类添加新项目
        const categoryKey = this.currentCategoryForSelector
        const newItem = this.createQuoteItemFromSelectedData(selectedData, categoryKey)
        
        if (!this.categorizedItems[categoryKey]) {
          this.$set(this.categorizedItems, categoryKey, [])
        }
        
        this.categorizedItems[categoryKey].push(newItem)
        
        // 重新计算总价
        if (categoryKey === 'serviceFee') {
          this.calculateServiceFeeTotal(newItem)
        } else {
          this.calculateItemTotal(newItem)
        }
      }

      this.dataSelectorVisible = false
      this.currentCategoryForSelector = null
      this.currentEditingRecord = null
      this.$message.success('已选择：' + selectedData.name)
    },

    // 从选择的数据创建报价项目
    createQuoteItemFromSelectedData(selectedData, categoryKey) {
      
      if (categoryKey === 'serviceFee') {
        return {
          id: uuidv4(),
          name: selectedData.name,
          category: categoryKey,
          serviceFeeType: 'fixed', // 从数据库选择的默认为固定金额
          serviceFeeValue: selectedData.price || null,
          unit: this.getDefaultUnitByCategory(categoryKey),
          quantity: 1,
          total: 0,
          remark: selectedData.priceRemark || '',
          bizType: selectedData.bizType,
          bizId: selectedData.id
        }
      } else {
        return {
          id: uuidv4(),
          name: selectedData.name,
          category: categoryKey,
          unitPrice: selectedData.price || null,
          unit: this.getDefaultUnitByCategory(categoryKey),
          quantity: this.getDefaultQuantityByCategory(categoryKey),
          total: 0,
          remark: selectedData.priceRemark || '',
          bizType: selectedData.bizType,
          bizId: selectedData.id
        }
      }
    },

    // 获取分类图标
    getCategoryIcon(categoryKey) {
      const iconMap = {
        scenic: 'environment',
        hotel: 'home',
        restaurant: 'coffee',
        traffic: 'car',
        guide: 'user',
        other: 'setting',
        serviceFee: 'dollar'
      }
      return iconMap[categoryKey] || 'file'
    },

    // 获取分类总价
    getCategoryTotal(categoryKey) {
      const items = this.categorizedItems[categoryKey] || []
      return items.reduce((sum, item) => sum + (item.total || 0), 0)
    },

    // 重置表单数据
    resetForm() {
      // 生成新的默认报价名称
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hour = String(now.getHours()).padStart(2, '0')
      const minute = String(now.getMinutes()).padStart(2, '0')
      const defaultQuoteName = `快捷报价-${year}${month}${day}${hour}${minute}`
      
      // 重置所有数据
      this.quoteName = defaultQuoteName
      this.personCount = 10
      this.finalRemark = ''
      this.originalContent = ''
      
      // 清空所有分类项目
      this.categorizedItems = {
        scenic: [],
        hotel: [],
        restaurant: [],
        traffic: [],
        guide: [],
        other: [],
        serviceFee: []
      }
      
      // 重置模态框状态
      this.pasteModalVisible = false
      this.tripModalVisible = false
      this.fileModalVisible = false
      this.mobileEditModalVisible = false
      this.isEditingQuoteName = false
      this.quickNumbersVisible = false
      this.dataSelectorVisible = false
      
      // 重置导入相关数据
      this.pasteContent = ''
      this.fileList = []
      this.editingItem = null
      this.tempQuoteName = ''
      this.currentCategoryForSelector = null
      this.currentEditingRecord = null
      
      // 重置加载状态
      this.importing = false
      this.saving = false
      this.updating = false
      this.loading = false
    }
  }
}
</script>

<style lang="less" scoped>
// 商务化设计变量
@primary-color: #1890ff;
@primary-light: #e6f7ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #ff4d4f;
@text-color: #262626;
@text-secondary: #8c8c8c;
@border-radius: 8px;
@card-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
@card-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.1);
@transition: all 0.3s ease;

.quick-quote-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f5f5f5;
  min-height: 100vh;

  // 商务化卡片样式
  .modern-card {
    border-radius: @border-radius;
    box-shadow: @card-shadow;
    border: 1px solid #f0f0f0;
    background: #fff;
    transition: @transition;
    
    &:hover {
      box-shadow: @card-shadow-hover;
      border-color: #d9d9d9;
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // 报价名称头部样式
  .quote-name-header {
    margin-bottom: 24px;

    .quote-name-input-like {
      background: #fff;
      border: 2px solid @primary-color;
      border-radius: @border-radius;
      cursor: pointer;
      transition: @transition;

      &:hover {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

              .quote-name-content {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          padding: 16px 24px;
          min-height: 56px;

          .quote-icon {
            color: @primary-color;
            font-size: 18px;
            flex-shrink: 0;
          }

          .quote-text {
            color: @text-color;
            font-size: 18px;
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            text-align: center;
          }

          .edit-icon {
            color: @text-secondary;
            font-size: 16px;
            transition: @transition;
            flex-shrink: 0;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            
            &:hover {
              color: @primary-color;
              background: rgba(24, 144, 255, 0.1);
              transform: scale(1.1);
            }
            
            &:active {
              transform: scale(0.95);
            }
          }
        }

      &:hover .quote-name-content .edit-icon {
        color: @primary-color;
      }
    }

    .quote-name-edit-input {
      .ant-input {
        border: 2px solid @primary-color;
        border-radius: @border-radius;
        box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1);
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        min-height: 60px;
        padding: 16px 24px;
      }
    }
  }

  // 数据来源卡片
  .source-card-wrapper {
    margin-bottom: 24px;

    .source-card {
      background: #fff;
      
      .section-header {
        text-align: center;
        margin-bottom: 24px;

        .section-title {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          margin: 0 0 8px 0;
          font-size: 20px;
          font-weight: 600;
          color: @text-color;

          .title-icon {
            font-size: 20px;
            color: @primary-color;
          }
        }

        .section-subtitle {
          margin: 0;
          font-size: 14px;
          color: @text-secondary;
        }
      }

      .source-buttons {
        .source-btn-wrapper {
          .source-btn {
            height: 100px;
            border: 1px solid #d9d9d9;
            border-radius: @border-radius;
            transition: @transition;

            &.paste-btn {
              background: @primary-color;
              border-color: @primary-color;
              color: #fff;
              
              &:hover {
                background: #40a9ff;
                border-color: #40a9ff;
                box-shadow: @card-shadow-hover;
              }
            }

            &.trip-btn {
              background: @success-color;
              border-color: @success-color;
              color: #fff;
              
              &:hover {
                background: #73d13d;
                border-color: #73d13d;
                box-shadow: @card-shadow-hover;
              }
            }

            &.file-btn {
              background: @warning-color;
              border-color: @warning-color;
              color: #fff;
              
              &:hover {
                background: #ffc53d;
                border-color: #ffc53d;
                box-shadow: @card-shadow-hover;
              }
            }

            &.blank-btn {
              background: #595959;
              border-color: #595959;
              color: #fff;
              
              &:hover {
                background: #8c8c8c;
                border-color: #8c8c8c;
                box-shadow: @card-shadow-hover;
              }
            }

            .btn-content {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100%;
              gap: 8px;

              .btn-icon {
                font-size: 24px;
                margin-bottom: 4px;
                transition: @transition;
              }

              .btn-text {
                font-size: 14px;
                font-weight: 600;
              }

              .btn-desc {
                font-size: 12px;
                opacity: 0.8;
              }
            }
          }
        }
      }
    }
  }

  // 基本信息卡片
  .basic-info-wrapper {
    margin-bottom: 24px;

    .basic-info-card {
      background: #fff;

      .basic-info-content {
        .info-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 20px;

          .info-icon {
            font-size: 18px;
            color: @primary-color;
          }

          .info-title {
            font-size: 16px;
            font-weight: 600;
            color: @text-color;
            margin: 0;
          }
        }

        .info-controls {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 20px;

          .person-count-section {
            display: flex;
            align-items: center;
            gap: 12px;

            .count-label {
              font-size: 16px;
              font-weight: 600;
              color: #34495e;
              margin: 0;
            }

            .person-count-container {
              position: relative;

              .person-count-input {
                width: 120px;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
                transition: @transition;

                &:focus {
                  border-color: @primary-color;
                  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1);
                }
              }

              .quick-numbers-panel {
                position: absolute;
                top: 100%;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1000;
                background: #fff;
                border-radius: @border-radius;
                box-shadow: @card-shadow-hover;
                padding: 16px;
                margin-top: 8px;
                min-width: 280px;
                max-width: calc(100vw - 48px);

                .quick-numbers-title {
                  font-size: 14px;
                  color: #7f8c8d;
                  margin-bottom: 12px;
                  text-align: center;
                  font-weight: 600;
                }

                .quick-numbers-grid {
                  display: grid;
                  grid-template-columns: repeat(4, 1fr);
                  gap: 8px;

                  .quick-number-item {
                    padding: 12px;
                    text-align: center;
                    font-size: 14px;
                    font-weight: 600;
                    border: 2px solid #ecf0f1;
                    border-radius: 8px;
                    cursor: pointer;
                    transition: @transition;
                    background: #fff;
                    color: #2c3e50;

                    &:hover {
                      border-color: @primary-color;
                      background: rgba(24, 144, 255, 0.05);
                      color: @primary-color;
                      transform: scale(1.05);
                    }

                    &.active {
                      border-color: @primary-color;
                      background: @primary-color;
                      color: #fff;
                      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
                    }
                  }
                }
              }
            }
          }

          .update-btn {
            border-radius: 8px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
            border: none;
            transition: @transition;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3);
            }
          }
        }
      }
    }
  }

  // 报价分类样式
  .quote-categories {
    .category-section {
      margin-bottom: 24px;

      .category-card {
        background: #fff;
        border-left: 4px solid #f0f0f0;
        transition: @transition;

        &:hover {
          border-left-color: @primary-color;
        }

        .category-header {
          margin-bottom: 16px;

          .category-title-wrapper {
            display: flex;
            align-items: center;
            gap: 8px;

            .category-icon {
              font-size: 18px;
              color: @primary-color;
            }

            .category-title {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              color: @text-color;
              display: flex;
              align-items: center;
              gap: 8px;
              
              .category-name {
                color: @text-color;
                font-weight: 600;
              }
              
              .category-per-person {
                font-size: 14px;
                font-weight: 500;
                color: @primary-color;
                background: rgba(24, 144, 255, 0.1);
                padding: 2px 8px;
                border-radius: 12px;
                white-space: nowrap;
              }
            }


          }
        }

        .desktop-table {
          .modern-table {
            border-radius: @border-radius;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

            .ant-table-thead > tr > th {
              background: #fafafa;
              color: @text-color;
              font-weight: 600;
              border-bottom: 2px solid #f0f0f0;
              text-align: center;
            }

            .ant-table-tbody > tr {
              transition: @transition;
              
              &:hover > td {
                background: rgba(24, 144, 255, 0.02) !important;
              }

              > td {
                border-bottom: 1px solid #f0f2f5;
                padding: 16px 12px;
              }
            }

            .table-input,
            .table-input-number,
            .table-select {
              border-radius: 6px;
              border: 1px solid #e8eaed;
              transition: @transition;

              &:focus,
              &:hover {
                border-color: @primary-color;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
              }
            }

            .total-price-cell {
              text-align: center;

              .total-price {
                font-weight: 600;
                font-size: 16px;
                color: #f5222d;
              }
            }

            .delete-btn {
              color: #e74c3c;
              transition: @transition;

              &:hover {
                color: #c0392b;
                background: rgba(231, 76, 60, 0.1);
                border-radius: 4px;
              }
            }

            .desktop-db-suffix-icon {
              color: #7f8c8d;
              cursor: pointer;
              padding: 6px;
              border-radius: 6px;
              transition: @transition;
              
              &:hover {
                color: @primary-color;
                background: rgba(24, 144, 255, 0.1);
                transform: scale(1.1);
              }

              &:active {
                transform: scale(0.95);
              }
            }
          }

          .modern-add-btn {
            margin-top: 16px;
            height: 40px;
            border: 1px dashed #d9d9d9;
            border-radius: @border-radius;
            color: @text-secondary;
            font-weight: 500;
            transition: @transition;
            background: #fff;
            
            &:hover {
              border-color: @primary-color;
              color: @primary-color;
              background: @primary-light;
            }
          }
        }

        .mobile-cards {
          .modern-mobile-item {
            margin-bottom: 12px;
            border-radius: @border-radius;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: @card-shadow;
            border: 1px solid transparent;
            transition: @transition;
            
            &:hover {
              box-shadow: @card-shadow-hover;
              transform: translateY(-2px);
              border-color: rgba(24, 144, 255, 0.2);
            }
            
            .mobile-item-main {
              display: flex;
              align-items: center;
              padding: 16px;
              cursor: pointer;
              transition: @transition;
              min-height: 60px;
              
              &:hover {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
              }
              
              &:active {
                transform: scale(0.98);
              }
            }
            
            .mobile-item-left {
              flex: 1;
              min-width: 0;
            }
            
            .mobile-item-name {
              font-size: 16px;
              font-weight: 600;
              color: #2c3e50;
              line-height: 1.4;
              margin-bottom: 4px;
              word-break: break-all;
              overflow-wrap: break-word;
            }
            
            .mobile-item-detail {
              font-size: 13px;
              color: #7f8c8d;
              line-height: 1.3;
              margin-bottom: 4px;
              font-weight: 500;
            }
            
            .mobile-item-remark {
              font-size: 12px;
              color: #95a5a6;
              line-height: 1.3;
              word-break: break-all;
              overflow-wrap: break-word;
              font-style: italic;
            }
            
            .mobile-item-right {
              display: flex;
              align-items: center;
              gap: 12px;
            }
            
            .mobile-item-total {
              font-size: 16px;
              font-weight: 600;
              color: #f5222d;
              white-space: nowrap;
            }
            
            .mobile-delete-btn {
              padding: 8px;
              color: #e74c3c;
              min-width: 36px;
              min-height: 36px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 8px;
              transition: @transition;
              
              &:hover {
                background: rgba(231, 76, 60, 0.1);
                transform: scale(1.1);
              }
              
              .anticon {
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }

  // 总计金额样式
  .total-wrapper {
    margin-bottom: 24px;

    .total-card {
      background: #fff;

      .total-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;

        .total-icon {
          font-size: 18px;
          color: @primary-color;
        }

        .total-title {
          font-size: 16px;
          font-weight: 600;
          margin: 0;
          color: @text-color;
        }
      }

              .total-section {
          margin-bottom: 20px;

          .total-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }



            &.per-person {
              font-size: 16px;
              font-weight: 500;
              color: @primary-color;
            }

            .total-label {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 14px;
              font-weight: 500;
              color: @text-color;

              .anticon {
                font-size: 16px;
              }
            }

            .total-value {
              font-size: 16px;
              font-weight: 600;
              color: @text-color;
            }
          }
        }

              .remark-section {
          margin-top: 20px;
          padding-top: 16px;
          border-top: 1px solid #f0f0f0;

          .remark-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;

            .remark-icon {
              font-size: 16px;
              color: @primary-color;
            }

            .remark-title {
              font-size: 14px;
              font-weight: 600;
              color: @text-color;
              margin: 0;
            }
          }

          .modern-textarea {
            background: #fff;
            border: 1px solid #d9d9d9;
            border-radius: @border-radius;
            
            &:focus {
              border-color: @primary-color;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }
          }
        }

              .action-section {
          margin-top: 24px;
          text-align: center;

          .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;

            .action-btn {
              border-radius: @border-radius;
              font-weight: 500;
              transition: @transition;
              min-width: 120px;

              &.export-btn,
              &.copy-btn {
                background: #fff;
                color: @text-color;
                border: 1px solid #d9d9d9;

                &:hover {
                  border-color: @primary-color;
                  color: @primary-color;
                }
              }

              &.save-btn {
                background: @primary-color;
                color: #fff;
                border: 1px solid @primary-color;

                &:hover {
                  background: #40a9ff;
                  border-color: #40a9ff;
                }
              }
            }
          }
        }
    }
  }

  // 原始内容样式
  .original-content-wrapper {
    margin-bottom: 24px;

    .original-content-card {
      .content-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;

        .content-icon {
          font-size: 24px;
          color: @primary-color;
        }

        .content-title {
          font-size: 20px;
          font-weight: 700;
          color: #2c3e50;
          margin: 0;
        }
      }

      .original-content {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: @border-radius;
        padding: 20px;
        max-height: 300px;
        overflow-y: auto;

        pre {
          margin: 0;
          white-space: pre-wrap;
          word-wrap: break-word;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 14px;
          line-height: 1.6;
          color: #495057;
        }
      }
    }
  }

  // 现代化模态框样式
      .modern-modal {
      .ant-modal-content {
        border-radius: @border-radius;
        box-shadow: @card-shadow-hover;
      }

      .ant-modal-header {
        background: #fff;
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 24px;

        .ant-modal-title {
          color: @text-color;
          font-weight: 600;
          font-size: 16px;
        }
      }

      .ant-modal-close {
        color: @text-secondary;
        
        &:hover {
          color: @text-color;
        }
      }

    .modern-textarea,
    .modern-input,
    .modern-input-number,
    .modern-select {
      border-radius: 8px;
      border: 1px solid #e8eaed;
      transition: @transition;

      &:focus,
      &:hover {
        border-color: @primary-color;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }

    .modern-upload {
      .upload-btn {
        border-radius: 8px;
        border: 1px solid #e8eaed;
        transition: @transition;

        &:hover {
          border-color: @primary-color;
          color: @primary-color;
        }
      }
    }

    &.mobile-edit-modal {
      .mobile-db-suffix-icon {
        color: #7f8c8d;
        cursor: pointer;
        padding: 6px;
        border-radius: 6px;
        transition: @transition;
        
        &:hover {
          color: @primary-color;
          background: rgba(24, 144, 255, 0.1);
          transform: scale(1.1);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .quick-quote-container {
    padding: 16px;
    background: #f5f5f5;

    // 移动端现代化卡片
    .modern-card {
      margin-bottom: 16px;
      border-radius: 8px;
      
      .ant-card-body {
        padding: 16px;
      }
    }

    // 移动端报价名称头部
    .quote-name-header {
      margin-bottom: 16px;
      
      .quote-name-input-like {
        min-height: 50px;
        border-radius: 8px;
        
        // 增加点击区域，改善移动端体验
        touch-action: manipulation;
        -webkit-tap-highlight-color: rgba(0,0,0,0);
        
        .quote-name-content {
          padding: 12px 16px;
          gap: 8px;
          min-height: 50px;
          
          .quote-icon {
            font-size: 16px;
            flex-shrink: 0;
          }
          
          .quote-text {
            font-size: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            text-align: center;
          }
          
          .edit-icon {
            font-size: 14px;
            flex-shrink: 0;
          }
        }
      }

      .quote-name-edit-input {
        .ant-input {
          min-height: 50px;
          padding: 12px 16px;
          font-size: 16px;
          border-radius: 8px;
        }
      }
    }

    // 移动端数据来源卡片
    .source-card-wrapper {
      margin-bottom: 16px;
      
      .source-card {
        .section-header {
          margin-bottom: 20px;
          
          .section-title {
            font-size: 20px;
            
            .title-icon {
              font-size: 24px;
            }
          }
          
          .section-subtitle {
            font-size: 14px;
          }
        }
        
        .source-buttons {
          .source-btn-wrapper {
            margin-bottom: 12px;
            
            .source-btn {
              height: 80px;
              border-radius: 8px;
              
              &.paste-btn {
                background: @primary-color;
                border-color: @primary-color;
                color: #fff;
                
                &:hover {
                  background: #40a9ff;
                  border-color: #40a9ff;
                }
              }

              &.trip-btn {
                background: @success-color;
                border-color: @success-color;
                color: #fff;
                
                &:hover {
                  background: #73d13d;
                  border-color: #73d13d;
                }
              }

              &.file-btn {
                background: @warning-color;
                border-color: @warning-color;
                color: #fff;
                
                &:hover {
                  background: #ffc53d;
                  border-color: #ffc53d;
                }
              }

              &.blank-btn {
                background: #595959;
                border-color: #595959;
                color: #fff;
                
                &:hover {
                  background: #8c8c8c;
                  border-color: #8c8c8c;
                }
              }
              
              .btn-content {
                gap: 6px;
                
                .btn-icon {
                  font-size: 24px;
                }
                
                .btn-text {
                  font-size: 14px;
                }
                
                .btn-desc {
                  font-size: 11px;
                  opacity: 0.8;
                }
              }
            }
          }
        }
      }
    }

    // 移动端基本信息
    .basic-info-wrapper {
      margin-bottom: 16px;
      
      .basic-info-card {
        .basic-info-content {
          .info-header {
            margin-bottom: 16px;
            
            .info-icon {
              font-size: 20px;
            }
            
            .info-title {
              font-size: 18px;
            }
          }
          
          .info-controls {
            flex-direction: column;
            gap: 16px;
            align-items: stretch;
            
            .person-count-section {
              justify-content: space-between;
              
              .count-label {
                font-size: 14px;
              }
              
              .person-count-container {
                .person-count-input {
                  width: 100px;
                }
                
                .quick-numbers-panel {
                  left: 50%;
                  right: auto;
                  transform: translateX(-50%);
                  min-width: 200px;
                  max-width: calc(100vw - 32px);
                  width: max-content;
                  
                  .quick-numbers-grid {
                    grid-template-columns: repeat(3, 1fr);
                    gap: 6px;
                    
                    .quick-number-item {
                      padding: 8px;
                      font-size: 12px;
                    }
                  }
                }
              }
            }
            
            .update-btn {
              width: 100%;
              height: 40px;
            }
          }
        }
      }
    }

    // 移动端分类样式
    .quote-categories {
      .category-section {
        margin-bottom: 16px;
        
        .category-card {
          .category-header {
            margin-bottom: 16px;
            
            .category-title-wrapper {
              .category-icon {
                font-size: 20px;
                padding: 6px;
              }
              
              .category-title {
                font-size: 18px;
                flex-wrap: wrap;
                
                .category-name {
                  font-size: 18px;
                }
                
                .category-per-person {
                  font-size: 13px;
                  padding: 1px 6px;
                  border-radius: 10px;
                }
              }
            }
          }
          
          .mobile-cards {
            .modern-mobile-item {
              margin-bottom: 8px;
              border-radius: 8px;
              
              .mobile-item-main {
                padding: 12px;
                min-height: 50px;
              }
              
              .mobile-item-name {
                font-size: 15px;
              }
              
              .mobile-item-detail {
                font-size: 12px;
              }
              
              .mobile-item-total {
                font-size: 16px;
              }
              
              .mobile-delete-btn {
                min-width: 32px;
                min-height: 32px;
                
                .anticon {
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
    }

    // 移动端总计样式
    .total-wrapper {
      margin-bottom: 16px;
      
      .total-card {
        .total-header {
          margin-bottom: 20px;
          padding-bottom: 12px;
          
          .total-icon {
            font-size: 24px;
          }
          
          .total-title {
            font-size: 20px;
          }
        }
        
        .total-section {
          margin-bottom: 20px;
          
          .total-item {
            padding: 12px 0;
            

            
            &.per-person {
              font-size: 16px;
            }
            
            .total-label {
              font-size: 14px;
              
              .anticon {
                font-size: 16px;
              }
            }
            
            .total-value {
              font-size: 16px;
            }
          }
        }
        
        .remark-section {
          margin-top: 20px;
          padding-top: 16px;
          
          .remark-header {
            margin-bottom: 12px;
            
            .remark-icon {
              font-size: 18px;
            }
            
            .remark-title {
              font-size: 16px;
            }
          }
        }
        
        .action-section {
          margin-top: 24px;
          
          .action-buttons {
            flex-direction: column;
            gap: 8px;
            
            .action-btn {
              width: 100%;
              min-width: auto;
              height: 44px;
              font-size: 15px;
            }
          }
        }
      }
    }

    // 移动端原始内容
    .original-content-wrapper {
      margin-bottom: 16px;
      
      .original-content-card {
        .content-header {
          margin-bottom: 12px;
          
          .content-icon {
            font-size: 20px;
          }
          
          .content-title {
            font-size: 18px;
          }
        }
        
        .original-content {
          padding: 16px;
          max-height: 200px;
          
          pre {
            font-size: 13px;
            line-height: 1.5;
          }
        }
      }
    }

    // 移动端模态框优化
    .modern-modal {
      .ant-modal-content {
        border-radius: 8px;
        margin: 16px;
      }
      
      .ant-modal-header {
        padding: 16px 20px;
        
        .ant-modal-title {
          font-size: 16px;
        }
      }
      
      .ant-modal-body {
        padding: 16px 20px;
      }
      
      &.mobile-edit-modal {
        .ant-form-item {
          margin-bottom: 12px;
        }
        
        .ant-form-item-label {
          font-size: 13px;
        }
        
        .modern-input,
        .modern-input-number,
        .modern-select,
        .modern-textarea {
          font-size: 14px;
          border-radius: 6px;
        }

        .mobile-db-suffix-icon {
          padding: 6px;
          border-radius: 6px;
        }
      }
    }

    // 移动端每行显示2个按钮
    @media (max-width: 576px) {
      .source-buttons {
        .ant-col {
          flex: 0 0 50%;
          max-width: 50%;
          padding: 0 4px;
        }
      }
      
      // 超小屏幕快捷选择优化
      .basic-info-card {
        .basic-info-content {
          .info-controls {
            .person-count-section {
              .person-count-container {
                .quick-numbers-panel {
                  min-width: 180px;
                  
                  .quick-numbers-grid {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 4px;
                    
                    .quick-number-item {
                      padding: 6px;
                      font-size: 11px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style> 