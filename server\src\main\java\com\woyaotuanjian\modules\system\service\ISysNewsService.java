package com.woyaotuanjian.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.woyaotuanjian.modules.system.entity.SysNews;
import java.util.List;

/**
 * <p>
 * 系统动态 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface ISysNewsService extends IService<SysNews> {
    
    /**
     * 根据用户角色获取可见的动态列表
     * @param roleCode 用户角色编码
     * @return 动态列表
     */
    List<SysNews> getVisibleNewsByRole(String roleCode);
    
} 