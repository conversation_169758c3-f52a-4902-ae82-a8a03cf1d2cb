/**
 * TinyMCE 编辑器统一配置
 */

// TinyMCE CDN 基础路径
export const TINYMCE_BASE_URL = 'https://cdn.woyaotuanjian.com/tinymce'

// 获取TinyMCE基础配置
export function getTinymceBaseConfig() {
  return {
    // 使用CDN皮肤和语言包
    skin: 'oxide',
    skin_url: `${TINYMCE_BASE_URL}/skins/ui/oxide`,
    content_css: `${TINYMCE_BASE_URL}/skins/content/default/content.css`,
    language: 'zh_CN',
    language_url: `${TINYMCE_BASE_URL}/langs/zh_CN.js`,
    // 使用CDN基础路径
    base_url: TINYMCE_BASE_URL,
    suffix: '.min',
    // 禁用云端加载
    external_plugins: {},
    forced_root_block: 'p',
    // 禁用API Key检查和品牌标识
    api_key: 'no-api-key',
    branding: false,
    promotion: false,
    // 通用样式
    content_style: `
      body { font-family:Microsoft YaHei,sans-serif; font-size:14px; }
    `,
    paste_as_text: true
  }
}

// 获取完整的编辑器配置
export function getTinymceConfig(options = {}) {
  const baseConfig = getTinymceBaseConfig()
  
  // 默认配置
  const defaultConfig = {
    height: 400,
    menubar: false,
    plugins: [
      'advlist autolink lists image',
      'searchreplace',
      'paste help wordcount'
    ],
    toolbar:
      'undo redo | formatselect | bold italic underline | ' +
      'alignleft aligncenter alignright | ' +
      'bullist numlist | image | removeformat',
    fontsize_formats: '12px 14px 16px 18px 24px',
    font_family_formats:
      '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;' +
      '宋体=simsun,serif;' +
      '黑体=SimHei,sans-serif;',
    automatic_uploads: true,
    paste_data_images: true
  }
  
  // 合并配置
  return {
    ...baseConfig,
    ...defaultConfig,
    ...options
  }
} 