<template>
  <a-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="素材名称" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
              <a-input 
                v-decorator="['materialName', validatorRules.materialName]" 
                placeholder="请输入素材名称" />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="素材类型" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
              <a-select 
                v-decorator="['materialType', validatorRules.materialType]" 
                placeholder="请选择素材类型"
                @change="handleTypeChange">
                <a-select-option value="1">图片素材</a-select-option>
                <a-select-option value="2">富文本素材</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="标签分类" :labelCol="{span: 3}" :wrapperCol="{span: 21}">
              <a-input 
                v-decorator="['tags']" 
                placeholder="多个标签用逗号分隔，如：风景,自然,旅游" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 图片素材上传 -->
        <div v-if="materialType === '1'">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="图片上传" :labelCol="{span: 3}" :wrapperCol="{span: 21}">
                <a-upload
                  name="file"
                  list-type="picture-card"
                  class="avatar-uploader"
                  :show-upload-list="false"
                  :action="uploadAction"
                  :headers="headers"
                  :before-upload="beforeUpload"
                  @change="handleImageChange">
                  <img v-if="imageUrl" :src="imageUrl" alt="素材图片" style="width: 100%; height: 100%; object-fit: cover;" />
                  <div v-else>
                    <a-icon :type="imageLoading ? 'loading' : 'plus'" />
                    <div class="ant-upload-text">上传图片</div>
                  </div>
                </a-upload>
                <div class="upload-tips">
                  <p>支持格式：JPG、PNG、GIF</p>
                  <p>图片大小：不超过5MB</p>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 富文本素材编辑 -->
        <div v-if="materialType === '2'">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="富文本内容" :labelCol="{span: 3}" :wrapperCol="{span: 21}">
                <Editor 
                  :key="editorKey" 
                  v-model="richtextContent" 
                  :init="editorConfig" />
                <div class="help-text">
                  <ul>
                    <li>支持调用135或秀米编辑器进行图文排版美化</li>
                    <li>135支持双向数据传递，秀米只支持回传</li>
                    <li>直接编辑或上传图片，远程图片会自动本地化</li>
                  </ul>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 预览区域已删除 -->

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'
  import Editor from '@tinymce/tinymce-vue'
  import { axios } from '@/utils/request'
  import { init135Editor } from '../../BizTrip/setting/135-dialog'
  import { initXiumiEditor } from '../../BizTrip/setting/xiumi-dialog'
  import Vue from 'vue'
  import { ACCESS_TOKEN } from '@/store/mutation-types'
  import { getTinymceConfig } from '@/config/tinymce.config' // 导入统一的TinyMCE配置
  
  // TinyMCE已在HTML中预加载，移除所有导入以避免远程加载

  export default {
    name: "BizMaterialModal",
    components: {
      Editor
    },
    data() {
      return {
        title: "操作",
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          materialName: {
            rules: [
              { required: true, message: '请输入素材名称!' },
              { min: 2, max: 50, message: '素材名称长度在2-50个字符之间!' }
            ]
          },
          materialType: {
            rules: [
              { required: true, message: '请选择素材类型!' }
            ]
          }
        },
        url: {
          add: "/biz/bizMaterial/add",
          edit: "/biz/bizMaterial/edit",
          queryById: "/biz/bizMaterial/queryById"
        },
        materialType: '1',
        imageUrl: '',
        imageLoading: false,
        uploadAction: '',
        headers: {},
        richtextContent: '',
        editorKey: 0,
        editorConfig: getTinymceConfig({
          height: 600,
          toolbar:
            'undo redo | formatselect | bold italic underline | ' +
            'alignleft aligncenter alignright | ' +
            'bullist numlist | image | removeformat | 135editor xiumi',
          images_upload_url: (axios.defaults.baseURL || '') + '/file/tinymce/upload',
          images_upload_credentials: true,
          setup: (editor) => {
            // 1. 初始化时设置默认内容
            editor.on('init', () => {
              if (this.richtextContent) {
                editor.setContent(this.richtextContent)
              }
            })

            // 2. 注册 135编辑器 按钮
            init135Editor(editor);
            
            // 3. 初始化秀米编辑器按钮
            initXiumiEditor(editor);

            // 添加内容变更监听
            editor.on('change input', () => {
              this.richtextContent = editor.getContent()
            })

            // 添加处理标记
            let isProcessingRemoteImages = false;

            // 修改内容处理器
            editor.on('BeforeSetContent', function (e) {
              // 如果内容包含远程图片，且不是初始化或正在处理中，则处理远程图片
              if (e.content.includes('<img') && !e.initial && !isProcessingRemoteImages) {
                e.preventDefault(); // 阻止默认的设置内容行为
                isProcessingRemoteImages = true; // 设置处理标记

                fetch((axios.defaults.baseURL || '') + '/file/tinymce/fetchRemote', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    content: e.content
                  }),
                  credentials: 'include'
                })
                  .then(response => response.json())
                  .then(result => {
                    if (result.success) {
                      // 素材编辑器中，135编辑器和秀米编辑器都是覆盖整个内容
                      editor.setContent(result.result.content);
                      // 触发change事件以确保v-model更新
                      editor.fire('change');
                      editor.fire('input');
                    }
                  })
                  .finally(() => {
                    isProcessingRemoteImages = false; // 重置处理标记
                  });
              }
            });
          }
        })
      }
    },
    created() {
      // 设置上传地址和请求头
      this.uploadAction = `${window._CONFIG['domianURL']}/file/upload`;
      this.headers = {
        'Authorization': 'Bearer ' + Vue.ls.get(ACCESS_TOKEN)
      };
    },
    methods: {
      add() {
        this.edit({});
      },
      edit(record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.materialType = record.materialType || '1';
        this.imageUrl = record.thumbnail || '';
        this.richtextContent = record.content || '';
        this.editorKey++; // 强制重新渲染编辑器
        
        if (this.model.id) {
          this.title = '编辑素材';
        } else {
          this.title = '新增素材';
        }
        
        // 设置表单值
        this.$nextTick(() => {
          this.form.setFieldsValue({
            materialName: this.model.materialName,
            materialType: this.model.materialType || '1',
            tags: this.model.tags
          });
        });
      },
      detail(record) {
        this.edit(record);
        this.title = '查看素材';
        // 设置为只读模式
        this.$nextTick(() => {
          this.form.getFieldsValue();
        });
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            
            // 准备提交数据
            let formData = Object.assign(this.model, values);
            
            // 根据素材类型设置内容
            if (this.materialType === '1') {
              if (!this.imageUrl) {
                this.$message.error('请上传图片!');
                that.confirmLoading = false;
                return;
              }
              formData.content = this.imageUrl;
              formData.thumbnail = this.imageUrl;
            } else {
              if (!this.richtextContent) {
                this.$message.error('请输入富文本内容!');
                that.confirmLoading = false;
                return;
              }
              formData.content = this.richtextContent;
              // 从富文本中提取第一张图片作为缩略图
              const imgMatch = this.richtextContent.match(/<img[^>]+src="([^"]+)"/);
              formData.thumbnail = imgMatch ? imgMatch[1] : '';
            }
            
            if (!this.model.id) {
              httpurl = this.url.add;
              method = 'post';
            } else {
              httpurl = this.url.edit;
              method = 'put';
            }
            
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.$emit('ok');
                that.close();
              } else {
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel() {
        this.close();
      },
      handleTypeChange(value) {
        this.materialType = value;
        // 清空之前的内容
        this.imageUrl = '';
        this.richtextContent = '';
        this.editorKey++; // 强制重新渲染编辑器
      },
      beforeUpload(file) {
        const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif';
        if (!isJpgOrPng) {
          this.$message.error('只能上传 JPG/PNG/GIF 格式的图片!');
          return false;
        }
        const isLt5M = file.size / 1024 / 1024 < 5;
        if (!isLt5M) {
          this.$message.error('图片大小不能超过 5MB!');
          return false;
        }
        return isJpgOrPng && isLt5M;
      },
      handleImageChange(info) {
        if (info.file.status === 'uploading') {
          this.imageLoading = true;
          return;
        }
        if (info.file.status === 'done') {
          this.imageLoading = false;
          if (info.file.response && info.file.response.success) {
            this.imageUrl = info.file.response.result;
            this.$message.success('图片上传成功!');
          } else {
            this.$message.error('图片上传失败!');
          }
        }
        if (info.file.status === 'error') {
          this.imageLoading = false;
          this.$message.error('图片上传失败!');
        }
      }
    }
  }
</script>

<style scoped>
  .avatar-uploader {
    display: block;
  }
  .avatar-uploader .ant-upload {
    width: 200px;
    height: 200px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .avatar-uploader .ant-upload:hover {
    border-color: #40a9ff;
  }
  .upload-tips {
    margin-top: 10px;
    color: #666;
    font-size: 12px;
  }
  .upload-tips p {
    margin: 2px 0;
  }
  .help-text {
    margin-top: 10px;
    color: #666;
    font-size: 12px;
  }
  .help-text ul {
    margin: 0;
    padding-left: 20px;
  }
  .help-text li {
    margin: 2px 0;
  }
  .content-preview {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
    background-color: #fafafa;
  }
</style>