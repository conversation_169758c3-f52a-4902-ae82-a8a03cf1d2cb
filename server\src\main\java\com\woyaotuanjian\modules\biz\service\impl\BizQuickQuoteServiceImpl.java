package com.woyaotuanjian.modules.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.woyaotuanjian.modules.biz.entity.BizQuickQuote;
import com.woyaotuanjian.modules.biz.entity.BizTrip;
import com.woyaotuanjian.modules.biz.entity.BizScenic;
import com.woyaotuanjian.modules.biz.entity.BizHotel;
import com.woyaotuanjian.modules.biz.entity.BizRestaurant;
import com.woyaotuanjian.modules.biz.entity.pojo.BizScenicExt;
import com.woyaotuanjian.modules.biz.entity.pojo.BizHotelExt;
import com.woyaotuanjian.modules.biz.entity.pojo.BizRestaurantExt;
import com.woyaotuanjian.modules.biz.mapper.BizQuickQuoteMapper;
import com.woyaotuanjian.modules.biz.mapper.BizTripMapper;
import com.woyaotuanjian.modules.biz.mapper.BizScenicMapper;
import com.woyaotuanjian.modules.biz.mapper.BizHotelMapper;
import com.woyaotuanjian.modules.biz.mapper.BizRestaurantMapper;
import com.woyaotuanjian.modules.biz.service.IBizQuickQuoteService;
import com.woyaotuanjian.modules.biz.service.IBizCompanyService;
import com.woyaotuanjian.modules.biz.service.AiService;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 快捷报价
 * @Author: System
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class BizQuickQuoteServiceImpl extends ServiceImpl<BizQuickQuoteMapper, BizQuickQuote> implements IBizQuickQuoteService {

    @Autowired
    private BizTripMapper tripMapper;
    
    @Autowired
    private BizScenicMapper scenicMapper;
    
    @Autowired
    private BizHotelMapper hotelMapper;
    
    @Autowired
    private BizRestaurantMapper restaurantMapper;
    
    @Autowired
    private AiService aiService;
    
    @Lazy
    @Autowired
    private IBizCompanyService companyService;

    @Override
    public JSONObject parsePastedContent(String content) {
        try {
            // 使用AI服务解析粘贴的内容
            String prompt = "请解析以下行程内容，提取出景点、酒店、餐厅等信息，只返回名称和类型，不需要时间安排：\n" + content;
            String aiResponse = aiService.parseImportedContent(prompt);
            
            JSONObject result = processAiQuoteData(aiResponse);
            // 保存原始内容
            result.put("originalContent", content);
            
            return result;
        } catch (Exception e) {
            log.error("解析粘贴内容失败", e);
            throw new RuntimeException("解析内容失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject importFromTrip(Long tripId) {
        try {
            BizTrip trip = tripMapper.selectById(tripId);
            if (trip == null) {
                throw new RuntimeException("行程不存在");
                    }
        
        JSONObject result = new JSONObject();
        
        // 按大类分组的报价项目
        JSONObject categorizedItems = new JSONObject();
        categorizedItems.put("scenic", new JSONArray());    // 景点
        categorizedItems.put("hotel", new JSONArray());     // 酒店
        categorizedItems.put("restaurant", new JSONArray()); // 就餐
        categorizedItems.put("traffic", new JSONArray());   // 车辆
        categorizedItems.put("guide", new JSONArray());     // 导游
        categorizedItems.put("other", new JSONArray());     // 其他
        categorizedItems.put("serviceFee", new JSONArray()); // 服务费
        
        // 解析行程的schedule字段
            if (trip.getSchedule() != null && !trip.getSchedule().isEmpty()) {
                JSONArray schedule = JSONArray.parseArray(trip.getSchedule());
                
                for (int i = 0; i < schedule.size(); i++) {
                    JSONObject item = schedule.getJSONObject(i);
                    String bizType = item.getString("bizType");
                    
                    if ("scenic".equals(bizType) || "hotel".equals(bizType) || "rest".equals(bizType)) {
                        JSONObject quoteItem = createQuoteItem(
                            item.getString("title"), 
                            mapBizTypeToCategory(bizType), 
                            bizType, 
                            item.getLong("bizId")
                        );
                        
                        // 尝试从数据库获取价格信息
                        fillPriceFromDatabase(quoteItem, bizType, item.getLong("bizId"));
                        
                        // 根据类型分类添加
                        String category = mapBizTypeToCategory(bizType);
                        categorizedItems.getJSONArray(category).add(quoteItem);
                    }
                }
            }
            
            // 添加默认项目（如果对应分类为空）
            addDefaultItemsIfEmpty(categorizedItems);
            
            result.put("categorizedItems", categorizedItems);
            result.put("quoteName", generateQuoteName(trip.getTripName()));
            
            return result;
        } catch (Exception e) {
            log.error("从行程导入失败", e);
            throw new RuntimeException("导入失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject importFromFile(String fileContent) {
        try {
            // 使用AI服务解析文件内容，和粘贴导入使用相同的逻辑
            String prompt = "请解析以下文档内容，提取出景点、酒店、餐厅等信息，只返回名称和类型，不需要时间安排：\n" + fileContent;
            String aiResponse = aiService.parseImportedContent(prompt);
            
            JSONObject result = processAiQuoteData(aiResponse);
            // 不设置原始内容，因为文件内容可能很长
            
            return result;
        } catch (Exception e) {
            log.error("解析文件内容失败", e);
            throw new RuntimeException("解析文件内容失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject processAiQuoteData(String aiResponseJson) {
        try {
            JSONObject responseObj = JSONObject.parseObject(aiResponseJson);
                    JSONObject result = new JSONObject();
        
        // 按大类分组的报价项目
        JSONObject categorizedItems = new JSONObject();
        categorizedItems.put("scenic", new JSONArray());    // 景点
        categorizedItems.put("hotel", new JSONArray());     // 酒店
        categorizedItems.put("restaurant", new JSONArray()); // 就餐
        categorizedItems.put("traffic", new JSONArray());   // 车辆
        categorizedItems.put("guide", new JSONArray());     // 导游
        categorizedItems.put("other", new JSONArray());     // 其他
        categorizedItems.put("serviceFee", new JSONArray()); // 服务费
        
        // 解析AI返回的数据
            if (responseObj.containsKey("schedule") && responseObj.get("schedule") instanceof JSONArray) {
                JSONArray scheduleArray = responseObj.getJSONArray("schedule");
                
                for (int i = 0; i < scheduleArray.size(); i++) {
                    JSONObject item = scheduleArray.getJSONObject(i);
                    String bizType = item.getString("bizType");
                    
                    if ("scenic".equals(bizType) || "hotel".equals(bizType) || "rest".equals(bizType)) {
                        // 通过名称查询bizId
                        Long bizId = findBizIdByName(item.getString("title"), bizType);
                        
                        JSONObject quoteItem = createQuoteItem(
                            item.getString("title"), 
                            mapBizTypeToCategory(bizType), 
                            bizType, 
                            bizId
                        );
                        
                        // 尝试从数据库获取价格信息
                        fillPriceFromDatabase(quoteItem, bizType, bizId);
                        
                        // 根据类型分类添加
                        String category = mapBizTypeToCategory(bizType);
                        categorizedItems.getJSONArray(category).add(quoteItem);
                    }
                }
            }
            
            // 添加默认项目（如果对应分类为空）
            addDefaultItemsIfEmpty(categorizedItems);
            
            result.put("categorizedItems", categorizedItems);
            
            // 生成报价名称
            String quoteName = generateQuoteNameFromJson(responseObj);
            result.put("quoteName", quoteName);
            
            return result;
        } catch (Exception e) {
            log.error("处理AI报价数据失败", e);
            throw new RuntimeException("处理数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 从数据库填充价格信息和标准名称
     */
    private void fillPriceFromDatabase(JSONObject quoteItem, String bizType, Long bizId) {
        if (bizId == null || bizId <= 0) {
            return;
        }
        
        try {
            switch (bizType) {
                case "scenic":
                    BizScenic scenic = scenicMapper.selectById(bizId);
                    if (scenic != null) {
                        // 更新为数据库中的标准名称
                        quoteItem.put("name", scenic.getScenicName());
                        // 更新价格信息
                        if (scenic.getPrice() != null) {
                            quoteItem.put("unitPrice", scenic.getPrice());
                            quoteItem.put("total", scenic.getPrice());
                        }
                        if (scenic.getPriceRemark() != null) {
                            quoteItem.put("remark", scenic.getPriceRemark());
                        }
                    }
                    break;
                case "hotel":
                    BizHotel hotel = hotelMapper.selectById(bizId);
                    if (hotel != null) {
                        // 更新为数据库中的标准名称
                        quoteItem.put("name", hotel.getHotelName());
                        // 更新价格信息
                        if (hotel.getPrice() != null) {
                            quoteItem.put("unitPrice", hotel.getPrice());
                            quoteItem.put("total", hotel.getPrice());
                        }
                        if (hotel.getPriceRemark() != null) {
                            quoteItem.put("remark", hotel.getPriceRemark());
                        }
                    }
                    break;
                case "rest":
                    BizRestaurant restaurant = restaurantMapper.selectById(bizId);
                    if (restaurant != null) {
                        // 更新为数据库中的标准名称
                        quoteItem.put("name", restaurant.getRestName());
                        // 更新价格信息
                        if (restaurant.getPrice() != null) {
                            quoteItem.put("unitPrice", restaurant.getPrice());
                            quoteItem.put("total", restaurant.getPrice());
                        }
                        if (restaurant.getPriceRemark() != null) {
                            quoteItem.put("remark", restaurant.getPriceRemark());
                        }
                    }
                    break;
            }
        } catch (Exception e) {
            log.warn("获取{}信息失败: {}", bizType, e.getMessage());
        }
    }
    
    /**
     * 带用户权限的验证辅助方法
     * @param title 要搜索的标题
     * @param bizType 业务类型
     * @return 过滤后的参数
     */
    private Map<String, Object> createFilteredParams(String title, String bizType) {
        // 创建参数
        Map<String, Object> param = new HashMap<>();
        
        // 设置搜索关键字
        if (title != null && !title.trim().isEmpty()) {
            param.put("search", title.trim());
        }
        
        // 如果是景区类型，设置type
        if ("scenic".equals(bizType)) {
            param.put("type", "scenic");
        }
        
        try {
            // 获取当前用户
            LoginUser sysUser = SysUserUtil.getCurrentUser();
            if (sysUser != null) {
                // 根据登录人过滤数据
                companyService.panelDataListFilter(param, sysUser);
                
                // 设置当前用户ID，确保当前用户数据优先
                param.put("currentUserId", sysUser.getId());
            }
        } catch (ClassCastException e) {
            // 捕获类加载器不同导致的类型转换异常
            log.warn("获取当前用户信息失败，可能是多线程环境下的类加载器问题: " + e.getMessage());
            // 不设置用户相关参数，继续使用基本搜索参数
        }
        
        return param;
    }

    /**
     * 通过名称查询bizId（带权限控制）
     */
    private Long findBizIdByName(String title, String bizType) {
        if (title == null || title.trim().isEmpty()) {
            return 0L;
        }
        
        title = title.trim();
        
        try {
            switch (bizType) {
                case "scenic":
                    return findScenicIdByNameWithPermission(title);
                case "hotel":
                    return findHotelIdByNameWithPermission(title);
                case "rest":
                    return findRestaurantIdByNameWithPermission(title);
                default:
                    return 0L;
            }
        } catch (Exception e) {
            log.warn("通过名称查询{}失败: {}", bizType, title, e);
            return 0L;
        }
    }
    
    /**
     * 带权限查询景区ID
     */
    private Long findScenicIdByNameWithPermission(String title) {
        try {
            // 创建带用户权限的查询参数
            Map<String, Object> param = createFilteredParams(title, "scenic");
            
            // 使用带权限的查询方法
            List<BizScenicExt> list = scenicMapper.getScenicPanelList(param);
            
            if (list == null || list.isEmpty()) {
                return 0L;
            }
            
            // 尝试精确匹配
            BizScenicExt scenic = null;
            for (BizScenicExt s : list) {
                if (s.getScenicName().equals(title) || 
                    (s.getScenicSimpleName() != null && s.getScenicSimpleName().equals(title))) {
                    scenic = s;
                    break;
                }
            }
            
            // 如果没有精确匹配，尝试模糊匹配
            if (scenic == null) {
                for (BizScenicExt s : list) {
                    if (s.getScenicName().contains(title) || 
                        (s.getScenicSimpleName() != null && s.getScenicSimpleName().contains(title))) {
                        scenic = s;
                        break;
                    }
                }
            }
            
            return scenic != null ? scenic.getId() : 0L;
        } catch (Exception e) {
            log.warn("带权限查询景区失败: " + title, e);
            return 0L;
        }
    }
    
    /**
     * 带权限查询酒店ID
     */
    private Long findHotelIdByNameWithPermission(String title) {
        try {
            // 创建带用户权限的查询参数
            Map<String, Object> param = createFilteredParams(title, null);
            
            // 使用带权限的查询方法
            List<BizHotelExt> list = hotelMapper.getHotelPanelList(param);
            
            if (list == null || list.isEmpty()) {
                return 0L;
            }
            
            // 尝试精确匹配
            BizHotelExt hotel = null;
            for (BizHotelExt h : list) {
                if (h.getHotelName().equals(title) || 
                    (h.getHotelSimpleName() != null && h.getHotelSimpleName().equals(title))) {
                    hotel = h;
                    break;
                }
            }
            
            // 如果没有精确匹配，尝试模糊匹配
            if (hotel == null) {
                for (BizHotelExt h : list) {
                    if (h.getHotelName().contains(title) || 
                        (h.getHotelSimpleName() != null && h.getHotelSimpleName().contains(title))) {
                        hotel = h;
                        break;
                    }
                }
            }
            
            return hotel != null ? hotel.getId() : 0L;
        } catch (Exception e) {
            log.warn("带权限查询酒店失败: " + title, e);
            return 0L;
        }
    }
    
    /**
     * 带权限查询餐厅ID
     */
    private Long findRestaurantIdByNameWithPermission(String title) {
        try {
            // 创建带用户权限的查询参数
            Map<String, Object> param = createFilteredParams(title, null);
            
            // 使用带权限的查询方法
            List<BizRestaurantExt> list = restaurantMapper.getRestPanelList(param);
            
            if (list == null || list.isEmpty()) {
                return 0L;
            }
            
            // 尝试精确匹配
            BizRestaurantExt restaurant = null;
            for (BizRestaurantExt r : list) {
                if (r.getRestName().equals(title) || 
                    (r.getRestSimpleName() != null && r.getRestSimpleName().equals(title))) {
                    restaurant = r;
                    break;
                }
            }
            
            // 如果没有精确匹配，尝试模糊匹配
            if (restaurant == null) {
                for (BizRestaurantExt r : list) {
                    if (r.getRestName().contains(title) || 
                        (r.getRestSimpleName() != null && r.getRestSimpleName().contains(title))) {
                        restaurant = r;
                        break;
                    }
                }
            }
            
            return restaurant != null ? restaurant.getId() : 0L;
        } catch (Exception e) {
            log.warn("带权限查询餐厅失败: " + title, e);
            return 0L;
        }
    }
    
    /**
     * 获取默认单位
     */
    private String getDefaultUnit(String bizType) {
        switch (bizType) {
            case "scenic":
                return "人";
            case "hotel":
                return "间";
            case "rest":
                return "人";
            default:
                return "项";
        }
    }
    
    /**
     * 映射业务类型到分类
     */
    private String mapBizTypeToCategory(String bizType) {
        switch (bizType) {
            case "scenic":
                return "scenic";
            case "hotel":
                return "hotel";
            case "rest":
                return "restaurant";
            case "traffic":
                return "traffic";
            case "commonFee":
                return "guide";
            default:
                return "other";
        }
    }
    
    /**
     * 创建报价项目
     */
    private JSONObject createQuoteItem(String name, String category, String bizType, Long bizId) {
        JSONObject quoteItem = new JSONObject();
        quoteItem.put("id", java.util.UUID.randomUUID().toString());
        quoteItem.put("name", name);
        quoteItem.put("category", category);
        quoteItem.put("bizType", bizType);
        quoteItem.put("bizId", bizId);
        quoteItem.put("unitPrice", BigDecimal.ZERO);
        quoteItem.put("quantity", getDefaultQuantity(category));
        quoteItem.put("unit", getDefaultUnitByCategory(category));
        quoteItem.put("total", BigDecimal.ZERO);
        quoteItem.put("remark", "");
        return quoteItem;
    }
    
    /**
     * 根据分类获取默认数量
     */
    private int getDefaultQuantity(String category) {
        switch (category) {
            case "scenic":
                return 1; // 门票按人数，在更新数量时处理
            case "hotel":
                return 1; // 酒店按房间数，在更新数量时处理
            case "restaurant":
                return 1; // 餐费按人数，在更新数量时处理
            case "traffic":
            case "guide":
                return 1; // 车辆、导游默认1个
            default:
                return 1;
        }
    }
    
    /**
     * 根据分类获取默认单位
     */
    private String getDefaultUnitByCategory(String category) {
        switch (category) {
            case "scenic":
                return "人";
            case "hotel":
                return "间";
            case "restaurant":
                return "人";
            case "traffic":
                return "辆";
            case "guide":
                return "人";
            default:
                return "项";
        }
    }
    
    /**
     * 添加默认项目（如果对应分类为空）
     */
    private void addDefaultItemsIfEmpty(JSONObject categorizedItems) {
        // 餐费
        if (categorizedItems.getJSONArray("restaurant").isEmpty()) {
            JSONObject restaurantItem = createQuoteItem("餐费", "restaurant", "rest", 0L);
            categorizedItems.getJSONArray("restaurant").add(restaurantItem);
        }
        
        // 车辆
        if (categorizedItems.getJSONArray("traffic").isEmpty()) {
            JSONObject trafficItem = createQuoteItem("车费", "traffic", "traffic", 0L);
            categorizedItems.getJSONArray("traffic").add(trafficItem);
        }
        
        // 导游
        if (categorizedItems.getJSONArray("guide").isEmpty()) {
            JSONObject guideItem = createQuoteItem("导游费", "guide", "commonFee", 0L);
            categorizedItems.getJSONArray("guide").add(guideItem);
        }
    }
    
    /**
     * 生成报价名称（JSONObject版本）
     */
    private String generateQuoteNameFromJson(JSONObject responseObj) {
        if (responseObj.containsKey("tripName")) {
            return generateQuoteName(responseObj.getString("tripName"));
        } else {
            return generateQuoteName(null);
        }
    }
    
    /**
     * 生成报价名称（String版本）
     */
    private String generateQuoteName(String baseName) {
        String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        
        if (baseName != null && !baseName.trim().isEmpty()) {
            return baseName + "报价-" + timestamp;
        } else {
            return "快捷报价-" + timestamp;
        }
    }
    
    /**
     * 查询景点列表（带权限控制）
     */
    @Override
    public JSONObject searchScenicList(String keyword, int pageNo, int pageSize) {
        try {
            // 创建带用户权限的查询参数
            Map<String, Object> param = createFilteredParams(keyword, "scenic");
            
            // 设置分页参数
            param.put("pageNo", pageNo);
            param.put("pageSize", pageSize);
            
            // 使用带权限的查询方法
            List<BizScenicExt> list = scenicMapper.getScenicPanelList(param);
            
            JSONObject result = new JSONObject();
            JSONArray items = new JSONArray();
            
            if (list != null && !list.isEmpty()) {
                for (BizScenicExt scenic : list) {
                    JSONObject item = new JSONObject();
                    item.put("id", scenic.getId());
                    item.put("name", scenic.getScenicName());
                    item.put("simpleName", scenic.getScenicSimpleName());
                    item.put("price", scenic.getPrice());
                    item.put("priceRemark", scenic.getPriceRemark());
                    item.put("location", scenic.getRegion());
                    item.put("type", "scenic");
                    items.add(item);
                }
            }
            
            result.put("items", items);
            result.put("total", items.size());
            result.put("pageNo", pageNo);
            result.put("pageSize", pageSize);
            
            return result;
        } catch (Exception e) {
            log.error("查询景点列表失败", e);
            throw new RuntimeException("查询景点列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询酒店列表（带权限控制）
     */
    @Override
    public JSONObject searchHotelList(String keyword, int pageNo, int pageSize) {
        try {
            // 创建带用户权限的查询参数
            Map<String, Object> param = createFilteredParams(keyword, null);
            
            // 设置分页参数
            param.put("pageNo", pageNo);
            param.put("pageSize", pageSize);
            
            // 使用带权限的查询方法
            List<BizHotelExt> list = hotelMapper.getHotelPanelList(param);
            
            JSONObject result = new JSONObject();
            JSONArray items = new JSONArray();
            
            if (list != null && !list.isEmpty()) {
                for (BizHotelExt hotel : list) {
                    JSONObject item = new JSONObject();
                    item.put("id", hotel.getId());
                    item.put("name", hotel.getHotelName());
                    item.put("simpleName", hotel.getHotelSimpleName());
                    item.put("price", hotel.getPrice());
                    item.put("priceRemark", hotel.getPriceRemark());
                    item.put("location", hotel.getRegion());
                    item.put("type", "hotel");
                    items.add(item);
                }
            }
            
            result.put("items", items);
            result.put("total", items.size());
            result.put("pageNo", pageNo);
            result.put("pageSize", pageSize);
            
            return result;
        } catch (Exception e) {
            log.error("查询酒店列表失败", e);
            throw new RuntimeException("查询酒店列表失败: " + e.getMessage());
        }
    }


    
} 