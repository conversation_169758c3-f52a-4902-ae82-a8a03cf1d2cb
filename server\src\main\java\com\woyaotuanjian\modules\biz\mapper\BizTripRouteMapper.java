package com.woyaotuanjian.modules.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.woyaotuanjian.modules.biz.entity.BizTripRoute;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 行程路线配置
 */
@Mapper
public interface BizTripRouteMapper extends BaseMapper<BizTripRoute> {

    /**
     * 根据行程ID获取所有路线配置
     */
    List<BizTripRoute> selectByTripId(@Param("tripId") Long tripId);
    
    /**
     * 根据起止点查询路线配置
     */
    BizTripRoute selectByRoute(@Param("tripId") Long tripId, 
                               @Param("originId") Long originId, 
                               @Param("originType") String originType,
                               @Param("destinationId") Long destinationId, 
                               @Param("destinationType") String destinationType);
} 