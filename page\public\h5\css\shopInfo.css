
#app{
  position: relative;
  background-color: #f5f7ff;
  box-sizing: border-box;
}

#loading{
  width: 100vw;
  height: 100vh;
  text-align: center;
  background-color: #fff;
  color:#0185FF;
  position: absolute;
  z-index: 9999;
}
#loading > div{
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}
.shop-info{
  margin: 0;
  padding: 0;
  width: 100%;
  display: block;
  position: relative;
}
.shop-info img{
  width: 100% !important;
}