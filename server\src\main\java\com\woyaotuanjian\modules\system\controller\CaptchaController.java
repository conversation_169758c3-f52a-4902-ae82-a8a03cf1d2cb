package com.woyaotuanjian.modules.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Random;
import java.util.UUID;

/**
 * 滑块验证码控制器
 */
@RestController
@RequestMapping("/sys/captcha")
@Api(tags = "滑块验证码")
@Slf4j
public class CaptchaController {

    @Autowired
    private RedisUtil redisUtil;

    private static final String CAPTCHA_PREFIX = "login_captcha_";
    private static final String CAPTCHA_VERIFIED = "captcha_verified_";
    private static final String LOGIN_FAIL_COUNT = "login_fail_count_";
    private static final String CAPTCHA_REQUEST_COUNT = "captcha_request_count_";
    private static final long CAPTCHA_EXPIRE_TIME = 5 * 60; // 5分钟
    private static final int MAX_CAPTCHA_REQUESTS_PER_HOUR = 20; // 每小时最多20次验证码请求

    /**
     * 获取验证码所需数据
     */
    @RequestMapping(value = "/slider", method = RequestMethod.GET)
    @ApiOperation("获取滑块验证码数据")
    public Result<JSONObject> getSliderCaptcha(HttpServletRequest request) {
        Result<JSONObject> result = new Result<>();
        
        try {
            String clientId = getClientIdentifier(request);
            
            // 检查验证码请求频率
            if (!checkCaptchaRequestLimit(clientId)) {
                result.error500("验证码请求过于频繁，请稍后再试");
                return result;
            }
            
            // 生成验证码ID
            String captchaId = UUID.randomUUID().toString();
            
            // 生成背景图片索引和滑块形状索引
            int backgroundIndex = generateRandomImageIndex("background");
            int blockShapeIndex = generateRandomImageIndex("block");
            
            // 生成随机的缺口位置（这个位置就是用户需要拖拽到的目标位置）
            int blockX = generateSecureBlockX();
            int blockY = generateSecureBlockY();
            
            // 生成随机的干扰元素
            String interferenceData = generateInterferenceData();
            
            // 存储验证码信息到Redis，用于后续验证
            JSONObject captchaInfo = new JSONObject();
            captchaInfo.put("blockX", blockX); // 真实的目标位置
            captchaInfo.put("blockY", blockY);
            captchaInfo.put("backgroundIndex", backgroundIndex);
            captchaInfo.put("blockShapeIndex", blockShapeIndex);
            captchaInfo.put("createTime", System.currentTimeMillis());
            captchaInfo.put("clientId", clientId);
            captchaInfo.put("interferenceData", interferenceData);
            
            // 将验证码信息存入Redis，设置过期时间
            redisUtil.set(CAPTCHA_PREFIX + captchaId, captchaInfo, CAPTCHA_EXPIRE_TIME);
            
            // 返回给前端的验证码数据（直接返回真实位置，但通过其他方式保证安全性）
            JSONObject responseData = new JSONObject();
            responseData.put("captchaId", captchaId);
            responseData.put("backgroundIndex", backgroundIndex); // 背景图片索引
            responseData.put("blockShapeIndex", blockShapeIndex); // 滑块形状索引
            responseData.put("blockY", blockY); // Y轴位置
            responseData.put("blockX", blockX); // X轴位置（真实位置，用户需要拖拽到这里）
            responseData.put("interferenceData", interferenceData); // 干扰数据
            responseData.put("imageWidth", 300); // 图片宽度
            responseData.put("imageHeight", 150); // 图片高度
            responseData.put("blockSize", 42); // 滑块大小
            
            result.setResult(responseData);
            result.setSuccess(true);
            
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            result.error500("生成验证码失败");
        }
        
        return result;
    }

    /**
     * 验证滑块位置
     */
    @RequestMapping(value = "/verify", method = RequestMethod.POST)
    @ApiOperation("验证滑块位置")
    public Result<Boolean> verifyCaptcha(@RequestBody JSONObject requestData, HttpServletRequest request) {
        
        Result<Boolean> result = new Result<>();
        
        try {
            // 从请求数据中提取参数
            String captchaId = requestData.getString("captchaId");
            Integer offsetX = requestData.getInteger("offsetX");
            Long startTime = requestData.getLong("startTime");
            String trajectory = requestData.getString("trajectory");
            
            // 参数验证
            if (captchaId == null || captchaId.isEmpty()) {
                result.error500("验证码ID不能为空");
                return result;
            }
            if (offsetX == null) {
                result.error500("滑块位置不能为空");
                return result;
            }
            
            String currentClientId = getClientIdentifier(request);
            
            // 从Redis获取验证码信息
            Object captchaObj = redisUtil.get(CAPTCHA_PREFIX + captchaId);
            if (captchaObj == null) {
                result.error500("验证码已过期或不存在");
                return result;
            }
            
            try {
                JSONObject captchaInfo;
                // 处理不同的数据类型
                if (captchaObj instanceof JSONObject) {
                    captchaInfo = (JSONObject) captchaObj;
                } else if (captchaObj instanceof java.util.Map) {
                    // 如果是Map类型（如LinkedHashMap），转换为JSONObject
                    captchaInfo = new JSONObject((java.util.Map<String, Object>) captchaObj);
                } else {
                    // 尝试将字符串解析为JSONObject
                    captchaInfo = JSONObject.parseObject(captchaObj.toString());
                }
                
                // 验证客户端绑定
                String originalClientId = captchaInfo.getString("clientId");
                if (originalClientId != null && !originalClientId.equals(currentClientId)) {
                    log.warn("验证码客户端不匹配: 原始={}, 当前={}", originalClientId, currentClientId);
                    result.error500("验证码无效");
                    return result;
                }
                
                // 验证时间间隔（防止过快或过慢的操作）
                Long createTime = captchaInfo.getLong("createTime");
                if (createTime != null) {
                    // 使用服务器端时间差，避免客户端时间误差影响
                    long serverTimeDiff = System.currentTimeMillis() - createTime;
                    if (serverTimeDiff < 300) { // 验证码创建后少于300ms就提交，可能是机器操作
                        log.warn("验证码操作时间过短: {}ms (服务器端时间差)", serverTimeDiff);
                        result.error500("操作过快，请重试");
                        return result;
                    }
                    if (serverTimeDiff > 300000) { // 超过5分钟认为超时（与验证码过期时间一致）
                        log.warn("验证码操作时间过长: {}ms (服务器端时间差)", serverTimeDiff);
                        result.error500("验证码已过期，请重新获取");
                        return result;
                    }
                    
                    // 可选：如果前端提供了startTime，进行额外的客户端时间一致性检查
                    if (startTime != null) {
                        long clientServerTimeDiff = Math.abs(startTime - createTime);
                        if (clientServerTimeDiff > 30000) { // 客户端与服务器时间差超过30秒，记录警告但不阻止
                            log.warn("客户端与服务器时间差异较大: {}ms, captchaId={}", clientServerTimeDiff, captchaId);
                        }
                    }
                }
                
                Integer blockXInteger = captchaInfo.getInteger("blockX");
                if (blockXInteger == null) {
                    log.error("验证码数据中缺少blockX字段: {}", captchaInfo.toJSONString());
                    result.error500("验证码数据异常");
                    return result;
                }
                
                int blockX = blockXInteger.intValue();
                
                // 验证轨迹合理性
                boolean trajectoryValid = validateTrajectory(trajectory, offsetX, startTime);
                if (!trajectoryValid) {
                    log.warn("验证码轨迹异常: captchaId={}, trajectory={}", captchaId, trajectory);
                    result.error500("操作轨迹异常，请重试");
                    return result;
                }
                
                // 动态计算误差范围
                int deviation = calculateDeviation(request);
                boolean positionValid = Math.abs(blockX - offsetX) <= deviation;
                
                // 综合验证结果
                boolean verified = positionValid && trajectoryValid;
                
                // 添加调试日志
                log.info("滑块验证: captchaId={}, 期望位置={}, 实际位置={}, 误差={}, 允许误差={}, 轨迹验证={}, 最终结果={}", 
                    captchaId, blockX, offsetX, Math.abs(blockX - offsetX), deviation, trajectoryValid, verified);
                
                // 无论验证是否成功，都删除原始验证码数据（一次性使用）
                redisUtil.del(CAPTCHA_PREFIX + captchaId);
                
                if (verified) {
                    // 验证通过，设置验证标记（较短的有效期，防止重复使用）
                    redisUtil.set(CAPTCHA_VERIFIED + captchaId, true, 2 * 60); // 2分钟有效期
                } else {
                    // 验证失败，记录失败次数
                    incrementLoginFailCount(request);
                }
                
                result.setSuccess(true);
                result.setResult(verified);
                
                if (!verified) {
                    result.setMessage("验证失败，请重试");
                }
                
            } catch (Exception e) {
                log.error("验证码数据格式错误", e);
                result.error500("验证码数据格式错误");
            }
            
        } catch (Exception e) {
            log.error("验证码验证失败", e);
            result.error500("验证码验证失败");
        }
        
        return result;
    }
    
    /**
     * 获取登录失败次数
     */
    @RequestMapping(value = "/failCount", method = RequestMethod.GET)
    @ApiOperation("获取登录失败次数")
    public Result<Integer> getLoginFailCount(HttpServletRequest request) {
        Result<Integer> result = new Result<>();
        
        try {
            String clientId = getClientIdentifier(request);
            Object countObj = redisUtil.get(LOGIN_FAIL_COUNT + clientId);
            int count = 0;
            
            if (countObj != null) {
                try {
                    count = (Integer) countObj;
                } catch (ClassCastException e) {
                    // 如果类型转换失败，尝试其他方式解析
                    if (countObj instanceof Number) {
                        count = ((Number) countObj).intValue();
                    } else if (countObj instanceof String) {
                        count = Integer.parseInt(countObj.toString());
                    }
                }
            }
            
            result.setSuccess(true);
            result.setResult(count);
            
        } catch (Exception e) {
            log.error("获取登录失败次数错误", e);
            result.error500("获取登录失败次数错误");
            result.setResult(0); // 出错时返回0次
        }
        
        return result;
    }
    
    /**
     * 增加登录失败次数
     */
    @RequestMapping(value = "/incrementFailCount", method = RequestMethod.POST)
    @ApiOperation("增加登录失败次数")
    public Result<Integer> incrementLoginFailCount(HttpServletRequest request) {
        Result<Integer> result = new Result<>();
        
        try {
            String clientId = getClientIdentifier(request);
            Object countObj = redisUtil.get(LOGIN_FAIL_COUNT + clientId);
            int count = 0;
            
            if (countObj != null) {
                try {
                    count = (Integer) countObj;
                } catch (ClassCastException e) {
                    // 如果类型转换失败，尝试其他方式解析
                    if (countObj instanceof Number) {
                        count = ((Number) countObj).intValue();
                    } else if (countObj instanceof String) {
                        count = Integer.parseInt(countObj.toString());
                    }
                }
            }
            
            count++;
            
            // 设置过期时间为24小时
            redisUtil.set(LOGIN_FAIL_COUNT + clientId, count, 24 * 60 * 60);
            
            result.setSuccess(true);
            result.setResult(count);
            
        } catch (Exception e) {
            log.error("增加登录失败次数错误", e);
            result.error500("增加登录失败次数错误");
            result.setResult(3); // 出错时返回3次，强制显示验证码
        }
        
        return result;
    }
    
    /**
     * 检查验证码是否已验证通过
     */
    @RequestMapping(value = "/checkVerified", method = RequestMethod.GET)
    @ApiOperation("检查验证码是否已验证通过")
    public Result<Boolean> checkCaptchaVerified(@RequestParam String captchaId) {
        Result<Boolean> result = new Result<>();
        
        try {
            Object verified = redisUtil.get(CAPTCHA_VERIFIED + captchaId);
            boolean isVerified = verified != null && (Boolean) verified;
            
            result.setSuccess(true);
            result.setResult(isVerified);
            
        } catch (Exception e) {
            log.error("检查验证码状态错误", e);
            result.error500("检查验证码状态错误");
            result.setResult(false);
        }
        
        return result;
    }
    
    /**
     * 清除登录失败次数（登录成功后调用）
     */
    @RequestMapping(value = "/clearFailCount", method = RequestMethod.POST)
    @ApiOperation("清除登录失败次数")
    public Result<Boolean> clearLoginFailCount(HttpServletRequest request) {
        Result<Boolean> result = new Result<>();
        
        try {
            String clientId = getClientIdentifier(request);
            redisUtil.del(LOGIN_FAIL_COUNT + clientId);
            
            result.setSuccess(true);
            result.setResult(true);
            
        } catch (Exception e) {
            log.error("清除登录失败次数错误", e);
            result.error500("清除登录失败次数错误");
            result.setResult(false);
        }
        
        return result;
    }
    

    
    /**
     * 动态计算验证码误差范围
     * 根据设备类型、User-Agent等因素智能调整
     * 增加了更大的容错范围，提升用户体验
     */
    private int calculateDeviation(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null) {
            return 10; // 默认误差，业界标准
        }
        
        userAgent = userAgent.toLowerCase();
        
        // 移动设备检测
        boolean isMobile = userAgent.contains("mobile") || 
                          userAgent.contains("android") || 
                          userAgent.contains("iphone") || 
                          userAgent.contains("ipad") ||
                          userAgent.contains("blackberry") ||
                          userAgent.contains("windows phone");
        
        // 平板设备检测
        boolean isTablet = userAgent.contains("ipad") || 
                          userAgent.contains("tablet") ||
                          (userAgent.contains("android") && !userAgent.contains("mobile"));
        
        // 根据设备类型设置误差，符合业界最佳实践
        if (isMobile && !isTablet) {
            // 手机：手指操作，容错范围12像素（业界标准8-15px）
            return 12;
        } else if (isTablet) {
            // 平板：介于手机和PC之间，容错范围10像素
            return 10;
        } else {
            // PC：鼠标操作，容错范围8像素（业界标准5-10px）
            return 8;
        }
    }

    /**
     * 获取更安全的客户端标识
     * 结合IP、User-Agent、X-Forwarded-For等多个因素
     */
    private String getClientIdentifier(HttpServletRequest request) {
        StringBuilder identifier = new StringBuilder();
        
        // 获取真实IP（防止简单的header伪造）
        String realIp = getRealClientIp(request);
        identifier.append(realIp);
        
        // 添加User-Agent指纹
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null && userAgent.length() > 10) {
            identifier.append("_").append(userAgent.hashCode());
        }
        
        // 添加Accept-Language指纹
        String acceptLang = request.getHeader("Accept-Language");
        if (acceptLang != null) {
            identifier.append("_").append(acceptLang.hashCode());
        }
        
        return identifier.toString();
    }
    
    /**
     * 更严格的IP获取方法
     */
    private String getRealClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        // 验证X-Forwarded-For的合法性
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            // 取第一个IP（最原始的客户端IP）
            if (ip.contains(",")) {
                ip = ip.split(",")[0].trim();
            }
            // 验证IP格式
            if (isValidIp(ip)) {
                return ip;
            }
        }
        
        // 依次尝试其他header
        String[] headers = {
            "Proxy-Client-IP", 
            "WL-Proxy-Client-IP", 
            "HTTP_CLIENT_IP", 
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String header : headers) {
            ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip) && isValidIp(ip)) {
                return ip;
            }
        }
        
        // 最后使用直连IP
        return request.getRemoteAddr();
    }
    
    /**
     * 验证IP地址格式
     */
    private boolean isValidIp(String ip) {
        if (ip == null || ip.isEmpty()) return false;
        
        // 简单的IP格式验证
        String[] parts = ip.split("\\.");
        if (parts.length != 4) return false;
        
        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) return false;
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 检查验证码请求频率限制
     */
    private boolean checkCaptchaRequestLimit(String clientId) {
        String key = CAPTCHA_REQUEST_COUNT + clientId;
        Object countObj = redisUtil.get(key);
        
        int count = 0;
        if (countObj != null) {
            try {
                count = (Integer) countObj;
            } catch (ClassCastException e) {
                if (countObj instanceof Number) {
                    count = ((Number) countObj).intValue();
                } else if (countObj instanceof String) {
                    count = Integer.parseInt(countObj.toString());
                }
            }
        }
        
        if (count >= MAX_CAPTCHA_REQUESTS_PER_HOUR) {
            return false;
        }
        
        // 增加计数，设置1小时过期
        redisUtil.set(key, count + 1, 60 * 60);
        return true;
    }
    
    /**
     * 生成更安全的随机位置
     * 结合时间因子和多重随机源
     */
    private int generateSecureBlockX() {
        // 使用当前时间作为种子的一部分
        long timeSeed = System.currentTimeMillis() / 1000; // 秒级时间戳
        
        // 结合多个随机源
        Random timeRandom = new Random(timeSeed);
        Random systemRandom = new Random();
        
        // 扩大随机范围，增加不可预测性
        int minX = 50 + timeRandom.nextInt(20); // 50-70
        int maxX = 200 + systemRandom.nextInt(40); // 200-240
        
        return minX + systemRandom.nextInt(maxX - minX);
    }
    
    /**
     * 生成随机的Y轴位置
     */
    private int generateSecureBlockY() {
        Random random = new Random();
        // Y轴位置相对固定，在图片中间区域
        int minY = 30;
        int maxY = 80;
        return minY + random.nextInt(maxY - minY);
    }
    
    /**
     * 生成随机图片索引
     */
    private int generateRandomImageIndex(String type) {
        Random random = new Random();
        // 假设有10张背景图和5张滑块形状
        if ("background".equals(type)) {
            return random.nextInt(10) + 1; // 1-10
        } else if ("block".equals(type)) {
            return random.nextInt(5) + 1; // 1-5
        }
        return 1;
    }
    

    
    /**
     * 生成干扰数据
     * 用于增加破解难度
     */
    private String generateInterferenceData() {
        Random random = new Random();
        StringBuilder interference = new StringBuilder();
        
        // 生成随机的干扰点坐标
        for (int i = 0; i < 5; i++) {
            int x = random.nextInt(300);
            int y = random.nextInt(150);
            interference.append(x).append(",").append(y);
            if (i < 4) {
                interference.append(";");
            }
        }
        
        return interference.toString();
    }
    
    /**
     * 验证滑动轨迹的合理性
     * 检查轨迹是否符合人类操作特征
     */
    private boolean validateTrajectory(String trajectory, Integer finalX, Long startTime) {
        if (trajectory == null || trajectory.isEmpty()) {
            // 轨迹数据是必需的，不允许降级验证
            // 这是为了防止攻击者仅通过控制时间来绕过验证
            log.warn("验证码验证缺少轨迹数据，拒绝验证 - 可能是自动化攻击");
            return false;
        }
        
        try {
            // 解析轨迹数据 格式: "x1,y1,t1;x2,y2,t2;..."
            String[] points = trajectory.split(";");
            if (points.length < 3) {
                // 轨迹点太少，可能是机器操作
                log.warn("轨迹点数量不足: {}", points.length);
                return false;
            }
            
            int lastX = 0;
            long lastTime = 0;
            boolean hasReverse = false; // 是否有回退动作
            int totalDistance = 0;
            long firstTime = 0;
            
            for (int i = 0; i < points.length; i++) {
                String[] point = points[i].split(",");
                if (point.length != 3) {
                    log.warn("轨迹点格式错误: {}", points[i]);
                    continue;
                }
                
                try {
                    int x = Integer.parseInt(point[0]);
                    int y = Integer.parseInt(point[1]);
                    long time = Long.parseLong(point[2]);
                    
                    if (i == 0) {
                        firstTime = time;
                    }
                    
                    if (i > 0) {
                        int deltaX = x - lastX;
                        long deltaTime = time - lastTime;
                        
                        // 检查是否有回退动作（人类操作特征）
                        if (deltaX < 0) {
                            hasReverse = true;
                        }
                        
                        // 检查移动速度是否合理
                        if (deltaTime > 0) {
                            double speed = Math.abs(deltaX) / (double) deltaTime;
                            if (speed > 2.0) { // 速度过快，可能是机器操作
                                log.warn("移动速度过快: {} px/ms", speed);
                                return false;
                            }
                        }
                        
                        totalDistance += Math.abs(deltaX);
                    }
                    
                    lastX = x;
                    lastTime = time;
                } catch (NumberFormatException e) {
                    log.warn("轨迹点数据解析失败: {}", points[i]);
                    return false;
                }
            }
            
            // 验证轨迹时间与提交时间的一致性
            // 注释掉客户端时间一致性检查，避免时间误差问题
            // if (startTime != null && firstTime > 0) {
            //     long timeDiff = Math.abs(startTime - firstTime);
            //     if (timeDiff > 1000) { // 时间差超过1秒，可能是伪造的轨迹
            //         log.warn("轨迹开始时间与提交时间不一致: {}ms", timeDiff);
            //         return false;
            //     }
            // }
            
            // 改为验证轨迹内部时间的连续性和合理性
            if (firstTime > 0 && lastTime > 0) {
                long trajectoryDuration = lastTime - firstTime;
                if (trajectoryDuration < 100) { // 轨迹持续时间过短，可能是伪造
                    log.warn("轨迹持续时间过短: {}ms", trajectoryDuration);
                    return false;
                }
                if (trajectoryDuration > 30000) { // 轨迹持续时间过长，可能异常
                    log.warn("轨迹持续时间过长: {}ms", trajectoryDuration);
                    return false;
                }
            }
            
            // 检查最终位置是否与提交的位置一致
            if (finalX != null && Math.abs(lastX - finalX) > 5) {
                log.warn("轨迹终点与提交位置不一致: 轨迹={}, 提交={}", lastX, finalX);
                return false;
            }
            
            // 检查总移动距离是否合理
            if (finalX != null && totalDistance < finalX * 0.8) {
                // 总移动距离太短，可能是直接跳跃
                log.warn("总移动距离过短: {} < {}", totalDistance, finalX * 0.8);
                return false;
            }
            
            // 检查轨迹的连续性和合理性
            if (points.length < 5) {
                log.warn("轨迹点数量过少，可能是机器操作: {}", points.length);
                return false;
            }
            
            // 人类操作通常会有一些回退调整动作，或者轨迹点足够多
            boolean isHumanLike = hasReverse || points.length > 15;
            if (!isHumanLike) {
                log.warn("轨迹缺乏人类操作特征: hasReverse={}, pointCount={}", hasReverse, points.length);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.warn("轨迹数据解析失败: {}", trajectory, e);
            return false;
        }
    }
} 