package com.woyaotuanjian.modules.biz.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 行程地图数据VO
 * @Author: Assistant
 * @Date: 2024/12/19
 * @Version: V1.0
 */
@Data
@ApiModel(value = "TripMapDataVO", description = "行程地图数据")
public class TripMapDataVO {

    @ApiModelProperty(value = "行程信息")
    private TripDetailVO tripInfo;

    @ApiModelProperty(value = "景点坐标列表")
    private List<ScenicCoordinateVO> scenicList;

    @ApiModelProperty(value = "酒店坐标列表")
    private List<HotelCoordinateVO> hotelList;

    @ApiModelProperty(value = "餐厅坐标列表")
    private List<RestaurantCoordinateVO> restaurantList;

    @ApiModelProperty(value = "常用语坐标列表")
    private List<WordCoordinateVO> wordList;

    /**
     * 景点坐标信息
     */
    @Data
    @ApiModel(value = "ScenicCoordinateVO", description = "景点坐标信息")
    public static class ScenicCoordinateVO {
        @ApiModelProperty(value = "景点ID")
        private Long id;

        @ApiModelProperty(value = "景点名称")
        private String scenicName;

        @ApiModelProperty(value = "经度")
        private Double longitude;

        @ApiModelProperty(value = "纬度")
        private Double latitude;

        @ApiModelProperty(value = "详细地址")
        private String regionAddress;
    }

    /**
     * 酒店坐标信息
     */
    @Data
    @ApiModel(value = "HotelCoordinateVO", description = "酒店坐标信息")
    public static class HotelCoordinateVO {
        @ApiModelProperty(value = "酒店ID")
        private Long id;

        @ApiModelProperty(value = "酒店名称")
        private String hotelName;

        @ApiModelProperty(value = "经度")
        private Double longitude;

        @ApiModelProperty(value = "纬度")
        private Double latitude;

        @ApiModelProperty(value = "详细地址")
        private String regionAddress;
    }

    /**
     * 餐厅坐标信息
     */
    @Data
    @ApiModel(value = "RestaurantCoordinateVO", description = "餐厅坐标信息")
    public static class RestaurantCoordinateVO {
        @ApiModelProperty(value = "餐厅ID")
        private Long id;

        @ApiModelProperty(value = "餐厅名称")
        private String restName;

        @ApiModelProperty(value = "经度")
        private Double longitude;

        @ApiModelProperty(value = "纬度")
        private Double latitude;

        @ApiModelProperty(value = "详细地址")
        private String regionAddress;
    }

    /**
     * 常用语坐标信息
     */
    @Data
    @ApiModel(value = "WordCoordinateVO", description = "常用语坐标信息")
    public static class WordCoordinateVO {
        @ApiModelProperty(value = "常用语ID")
        private Long id;

        @ApiModelProperty(value = "常用语标题")
        private String wordTitle;

        @ApiModelProperty(value = "经度")
        private Double longitude;

        @ApiModelProperty(value = "纬度")
        private Double latitude;

        @ApiModelProperty(value = "常用语场景")
        private String wordScence;
    }
} 