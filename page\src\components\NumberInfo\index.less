@import "../index";

@numberInfo-prefix-cls: ~"@{ant-pro-prefix}-number-info";

.@{numberInfo-prefix-cls} {

  .ant-pro-number-info-subtitle {
    color: @text-color-secondary;
    font-size: @font-size-base;
    height: 22px;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
  }

  .number-info-value {
    margin-top: 4px;
    font-size: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;

    & > span {
      color: @heading-color;
      display: inline-block;
      line-height: 32px;
      height: 32px;
      font-size: 24px;
      margin-right: 32px;
    }

    .sub-total {
      color: @text-color-secondary;
      font-size: @font-size-lg;
      vertical-align: top;
      margin-right: 0;
      i {
        font-size: 12px;
        transform: scale(0.82);
        margin-left: 4px;
      }
      :global(.anticon-caret-up) {
        color: @red-6;
      }
      :global(.anticon-caret-down) {
        color: @green-6;
      }
    }
  }
}